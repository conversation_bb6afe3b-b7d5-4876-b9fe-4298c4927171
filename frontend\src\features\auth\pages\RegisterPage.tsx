import {FormEvent, useState} from 'react';
import {Link, useNavigate} from 'react-router-dom';
import {useAuth} from '@shared/auth/AuthContext';

export default function RegisterPage() {
  const navigate = useNavigate();
  const {register} = useAuth();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [dob, setDob] = useState('');
  const [error, setError] = useState<string | null>(null);

  async function onSubmit(e: FormEvent) {
    e.preventDefault();
    setError(null);
    try {
      const dobIso = dob ? new Date(dob).toISOString() : '';
      await register({
        email,
        username: username || undefined,
        password,
        first_name: firstName,
        last_name: lastName,
        date_of_birth: dobIso,
      });
      navigate('/materials', {replace: true});
    } catch (err: any) {
      const detail = err?.response?.data?.detail || 'Registration failed';
      setError(Array.isArray(detail) ? detail[0]?.msg ?? 'Registration failed' : detail);
    }
  }

  return (
    <div className="container" style={{maxWidth: 520, marginTop: 32}}>
      <h2>Create your parent account</h2>
      {error && (
        <div className="alert alert-error" role="alert" style={{marginBottom: 12}}>
          {error}
        </div>
      )}
      <form onSubmit={onSubmit} className="card" style={{padding: 16}}>
        <div style={{display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12}}>
          <label className="form-label">
            First name
            <input
              className="form-input"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              required
            />
          </label>
          <label className="form-label">
            Last name
            <input
              className="form-input"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              required
            />
          </label>
        </div>
        <label className="form-label">
          Email
          <input
            className="form-input"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </label>
        <label className="form-label">
          Username (optional)
          <input
            className="form-input"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Choose a unique username"
          />
        </label>
        <label className="form-label">
          Password
          <input
            className="form-input"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            minLength={8}
            required
          />
        </label>
        <label className="form-label">
          Date of birth
          <input
            className="form-input"
            type="date"
            value={dob}
            onChange={(e) => setDob(e.target.value)}
            required
          />
        </label>
        <button className="btn btn-primary" type="submit" style={{marginTop: 12}}>
          Create account
        </button>
      </form>
      <p style={{marginTop: 12}}>
        Already have an account? <Link to="/login">Sign in</Link>
      </p>
    </div>
  );
}


