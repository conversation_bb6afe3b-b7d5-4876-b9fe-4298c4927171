function ty(l,u){for(var r=0;r<u.length;r++){const o=u[r];if(typeof o!="string"&&!Array.isArray(o)){for(const s in o)if(s!=="default"&&!(s in l)){const p=Object.getOwnPropertyDescriptor(o,s);p&&Object.defineProperty(l,s,p.get?p:{enumerable:!0,get:()=>o[s]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const p of s)if(p.type==="childList")for(const m of p.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&o(m)}).observe(document,{childList:!0,subtree:!0});function r(s){const p={};return s.integrity&&(p.integrity=s.integrity),s.referrerPolicy&&(p.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?p.credentials="include":s.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function o(s){if(s.ep)return;s.ep=!0;const p=r(s);fetch(s.href,p)}})();function Pr(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var xr={exports:{}},pi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fd;function ey(){if(Fd)return pi;Fd=1;var l=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function r(o,s,p){var m=null;if(p!==void 0&&(m=""+p),s.key!==void 0&&(m=""+s.key),"key"in s){p={};for(var g in s)g!=="key"&&(p[g]=s[g])}else p=s;return s=p.ref,{$$typeof:l,type:o,key:m,ref:s!==void 0?s:null,props:p}}return pi.Fragment=u,pi.jsx=r,pi.jsxs=r,pi}var $d;function ay(){return $d||($d=1,xr.exports=ey()),xr.exports}var B=ay(),br={exports:{}},ct={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wd;function ny(){if(Wd)return ct;Wd=1;var l=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),p=Symbol.for("react.consumer"),m=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),A=Symbol.iterator;function N(b){return b===null||typeof b!="object"?null:(b=A&&b[A]||b["@@iterator"],typeof b=="function"?b:null)}var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,L={};function U(b,H,Q){this.props=b,this.context=H,this.refs=L,this.updater=Q||k}U.prototype.isReactComponent={},U.prototype.setState=function(b,H){if(typeof b!="object"&&typeof b!="function"&&b!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,b,H,"setState")},U.prototype.forceUpdate=function(b){this.updater.enqueueForceUpdate(this,b,"forceUpdate")};function W(){}W.prototype=U.prototype;function J(b,H,Q){this.props=b,this.context=H,this.refs=L,this.updater=Q||k}var Z=J.prototype=new W;Z.constructor=J,M(Z,U.prototype),Z.isPureReactComponent=!0;var tt=Array.isArray,Y={H:null,A:null,T:null,S:null,V:null},ft=Object.prototype.hasOwnProperty;function ot(b,H,Q,G,F,mt){return Q=mt.ref,{$$typeof:l,type:b,key:H,ref:Q!==void 0?Q:null,props:mt}}function Tt(b,H){return ot(b.type,H,void 0,void 0,void 0,b.props)}function xt(b){return typeof b=="object"&&b!==null&&b.$$typeof===l}function Yt(b){var H={"=":"=0",":":"=2"};return"$"+b.replace(/[=:]/g,function(Q){return H[Q]})}var Gt=/\/+/g;function Bt(b,H){return typeof b=="object"&&b!==null&&b.key!=null?Yt(""+b.key):H.toString(36)}function Oe(){}function Mt(b){switch(b.status){case"fulfilled":return b.value;case"rejected":throw b.reason;default:switch(typeof b.status=="string"?b.then(Oe,Oe):(b.status="pending",b.then(function(H){b.status==="pending"&&(b.status="fulfilled",b.value=H)},function(H){b.status==="pending"&&(b.status="rejected",b.reason=H)})),b.status){case"fulfilled":return b.value;case"rejected":throw b.reason}}throw b}function bt(b,H,Q,G,F){var mt=typeof b;(mt==="undefined"||mt==="boolean")&&(b=null);var I=!1;if(b===null)I=!0;else switch(mt){case"bigint":case"string":case"number":I=!0;break;case"object":switch(b.$$typeof){case l:case u:I=!0;break;case x:return I=b._init,bt(I(b._payload),H,Q,G,F)}}if(I)return F=F(b),I=G===""?"."+Bt(b,0):G,tt(F)?(Q="",I!=null&&(Q=I.replace(Gt,"$&/")+"/"),bt(F,H,Q,"",function(Ne){return Ne})):F!=null&&(xt(F)&&(F=Tt(F,Q+(F.key==null||b&&b.key===F.key?"":(""+F.key).replace(Gt,"$&/")+"/")+I)),H.push(F)),1;I=0;var ie=G===""?".":G+":";if(tt(b))for(var zt=0;zt<b.length;zt++)G=b[zt],mt=ie+Bt(G,zt),I+=bt(G,H,Q,mt,F);else if(zt=N(b),typeof zt=="function")for(b=zt.call(b),zt=0;!(G=b.next()).done;)G=G.value,mt=ie+Bt(G,zt++),I+=bt(G,H,Q,mt,F);else if(mt==="object"){if(typeof b.then=="function")return bt(Mt(b),H,Q,G,F);throw H=String(b),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(b).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return I}function w(b,H,Q){if(b==null)return b;var G=[],F=0;return bt(b,G,"","",function(mt){return H.call(Q,mt,F++)}),G}function X(b){if(b._status===-1){var H=b._result;H=H(),H.then(function(Q){(b._status===0||b._status===-1)&&(b._status=1,b._result=Q)},function(Q){(b._status===0||b._status===-1)&&(b._status=2,b._result=Q)}),b._status===-1&&(b._status=0,b._result=H)}if(b._status===1)return b._result.default;throw b._result}var P=typeof reportError=="function"?reportError:function(b){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof b=="object"&&b!==null&&typeof b.message=="string"?String(b.message):String(b),error:b});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",b);return}console.error(b)};function ut(){}return ct.Children={map:w,forEach:function(b,H,Q){w(b,function(){H.apply(this,arguments)},Q)},count:function(b){var H=0;return w(b,function(){H++}),H},toArray:function(b){return w(b,function(H){return H})||[]},only:function(b){if(!xt(b))throw Error("React.Children.only expected to receive a single React element child.");return b}},ct.Component=U,ct.Fragment=r,ct.Profiler=s,ct.PureComponent=J,ct.StrictMode=o,ct.Suspense=y,ct.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Y,ct.__COMPILER_RUNTIME={__proto__:null,c:function(b){return Y.H.useMemoCache(b)}},ct.cache=function(b){return function(){return b.apply(null,arguments)}},ct.cloneElement=function(b,H,Q){if(b==null)throw Error("The argument must be a React element, but you passed "+b+".");var G=M({},b.props),F=b.key,mt=void 0;if(H!=null)for(I in H.ref!==void 0&&(mt=void 0),H.key!==void 0&&(F=""+H.key),H)!ft.call(H,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&H.ref===void 0||(G[I]=H[I]);var I=arguments.length-2;if(I===1)G.children=Q;else if(1<I){for(var ie=Array(I),zt=0;zt<I;zt++)ie[zt]=arguments[zt+2];G.children=ie}return ot(b.type,F,void 0,void 0,mt,G)},ct.createContext=function(b){return b={$$typeof:m,_currentValue:b,_currentValue2:b,_threadCount:0,Provider:null,Consumer:null},b.Provider=b,b.Consumer={$$typeof:p,_context:b},b},ct.createElement=function(b,H,Q){var G,F={},mt=null;if(H!=null)for(G in H.key!==void 0&&(mt=""+H.key),H)ft.call(H,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(F[G]=H[G]);var I=arguments.length-2;if(I===1)F.children=Q;else if(1<I){for(var ie=Array(I),zt=0;zt<I;zt++)ie[zt]=arguments[zt+2];F.children=ie}if(b&&b.defaultProps)for(G in I=b.defaultProps,I)F[G]===void 0&&(F[G]=I[G]);return ot(b,mt,void 0,void 0,null,F)},ct.createRef=function(){return{current:null}},ct.forwardRef=function(b){return{$$typeof:g,render:b}},ct.isValidElement=xt,ct.lazy=function(b){return{$$typeof:x,_payload:{_status:-1,_result:b},_init:X}},ct.memo=function(b,H){return{$$typeof:v,type:b,compare:H===void 0?null:H}},ct.startTransition=function(b){var H=Y.T,Q={};Y.T=Q;try{var G=b(),F=Y.S;F!==null&&F(Q,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(ut,P)}catch(mt){P(mt)}finally{Y.T=H}},ct.unstable_useCacheRefresh=function(){return Y.H.useCacheRefresh()},ct.use=function(b){return Y.H.use(b)},ct.useActionState=function(b,H,Q){return Y.H.useActionState(b,H,Q)},ct.useCallback=function(b,H){return Y.H.useCallback(b,H)},ct.useContext=function(b){return Y.H.useContext(b)},ct.useDebugValue=function(){},ct.useDeferredValue=function(b,H){return Y.H.useDeferredValue(b,H)},ct.useEffect=function(b,H,Q){var G=Y.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(b,H)},ct.useId=function(){return Y.H.useId()},ct.useImperativeHandle=function(b,H,Q){return Y.H.useImperativeHandle(b,H,Q)},ct.useInsertionEffect=function(b,H){return Y.H.useInsertionEffect(b,H)},ct.useLayoutEffect=function(b,H){return Y.H.useLayoutEffect(b,H)},ct.useMemo=function(b,H){return Y.H.useMemo(b,H)},ct.useOptimistic=function(b,H){return Y.H.useOptimistic(b,H)},ct.useReducer=function(b,H,Q){return Y.H.useReducer(b,H,Q)},ct.useRef=function(b){return Y.H.useRef(b)},ct.useState=function(b){return Y.H.useState(b)},ct.useSyncExternalStore=function(b,H,Q){return Y.H.useSyncExternalStore(b,H,Q)},ct.useTransition=function(){return Y.H.useTransition()},ct.version="19.1.1",ct}var Pd;function Ir(){return Pd||(Pd=1,br.exports=ny()),br.exports}var D=Ir();const Xm=Pr(D),ly=ty({__proto__:null,default:Xm},[D]);var Sr={exports:{}},di={},Er={exports:{}},Tr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Id;function iy(){return Id||(Id=1,function(l){function u(w,X){var P=w.length;w.push(X);t:for(;0<P;){var ut=P-1>>>1,b=w[ut];if(0<s(b,X))w[ut]=X,w[P]=b,P=ut;else break t}}function r(w){return w.length===0?null:w[0]}function o(w){if(w.length===0)return null;var X=w[0],P=w.pop();if(P!==X){w[0]=P;t:for(var ut=0,b=w.length,H=b>>>1;ut<H;){var Q=2*(ut+1)-1,G=w[Q],F=Q+1,mt=w[F];if(0>s(G,P))F<b&&0>s(mt,G)?(w[ut]=mt,w[F]=P,ut=F):(w[ut]=G,w[Q]=P,ut=Q);else if(F<b&&0>s(mt,P))w[ut]=mt,w[F]=P,ut=F;else break t}}return X}function s(w,X){var P=w.sortIndex-X.sortIndex;return P!==0?P:w.id-X.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var p=performance;l.unstable_now=function(){return p.now()}}else{var m=Date,g=m.now();l.unstable_now=function(){return m.now()-g}}var y=[],v=[],x=1,A=null,N=3,k=!1,M=!1,L=!1,U=!1,W=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,Z=typeof setImmediate<"u"?setImmediate:null;function tt(w){for(var X=r(v);X!==null;){if(X.callback===null)o(v);else if(X.startTime<=w)o(v),X.sortIndex=X.expirationTime,u(y,X);else break;X=r(v)}}function Y(w){if(L=!1,tt(w),!M)if(r(y)!==null)M=!0,ft||(ft=!0,Bt());else{var X=r(v);X!==null&&bt(Y,X.startTime-w)}}var ft=!1,ot=-1,Tt=5,xt=-1;function Yt(){return U?!0:!(l.unstable_now()-xt<Tt)}function Gt(){if(U=!1,ft){var w=l.unstable_now();xt=w;var X=!0;try{t:{M=!1,L&&(L=!1,J(ot),ot=-1),k=!0;var P=N;try{e:{for(tt(w),A=r(y);A!==null&&!(A.expirationTime>w&&Yt());){var ut=A.callback;if(typeof ut=="function"){A.callback=null,N=A.priorityLevel;var b=ut(A.expirationTime<=w);if(w=l.unstable_now(),typeof b=="function"){A.callback=b,tt(w),X=!0;break e}A===r(y)&&o(y),tt(w)}else o(y);A=r(y)}if(A!==null)X=!0;else{var H=r(v);H!==null&&bt(Y,H.startTime-w),X=!1}}break t}finally{A=null,N=P,k=!1}X=void 0}}finally{X?Bt():ft=!1}}}var Bt;if(typeof Z=="function")Bt=function(){Z(Gt)};else if(typeof MessageChannel<"u"){var Oe=new MessageChannel,Mt=Oe.port2;Oe.port1.onmessage=Gt,Bt=function(){Mt.postMessage(null)}}else Bt=function(){W(Gt,0)};function bt(w,X){ot=W(function(){w(l.unstable_now())},X)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(w){w.callback=null},l.unstable_forceFrameRate=function(w){0>w||125<w?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Tt=0<w?Math.floor(1e3/w):5},l.unstable_getCurrentPriorityLevel=function(){return N},l.unstable_next=function(w){switch(N){case 1:case 2:case 3:var X=3;break;default:X=N}var P=N;N=X;try{return w()}finally{N=P}},l.unstable_requestPaint=function(){U=!0},l.unstable_runWithPriority=function(w,X){switch(w){case 1:case 2:case 3:case 4:case 5:break;default:w=3}var P=N;N=w;try{return X()}finally{N=P}},l.unstable_scheduleCallback=function(w,X,P){var ut=l.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?ut+P:ut):P=ut,w){case 1:var b=-1;break;case 2:b=250;break;case 5:b=1073741823;break;case 4:b=1e4;break;default:b=5e3}return b=P+b,w={id:x++,callback:X,priorityLevel:w,startTime:P,expirationTime:b,sortIndex:-1},P>ut?(w.sortIndex=P,u(v,w),r(y)===null&&w===r(v)&&(L?(J(ot),ot=-1):L=!0,bt(Y,P-ut))):(w.sortIndex=b,u(y,w),M||k||(M=!0,ft||(ft=!0,Bt()))),w},l.unstable_shouldYield=Yt,l.unstable_wrapCallback=function(w){var X=N;return function(){var P=N;N=X;try{return w.apply(this,arguments)}finally{N=P}}}}(Tr)),Tr}var tm;function uy(){return tm||(tm=1,Er.exports=iy()),Er.exports}var Ar={exports:{}},oe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var em;function cy(){if(em)return oe;em=1;var l=Ir();function u(y){var v="https://react.dev/errors/"+y;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)v+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+y+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(u(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},s=Symbol.for("react.portal");function p(y,v,x){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:A==null?null:""+A,children:y,containerInfo:v,implementation:x}}var m=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,v){if(y==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return oe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,oe.createPortal=function(y,v){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(u(299));return p(y,v,null,x)},oe.flushSync=function(y){var v=m.T,x=o.p;try{if(m.T=null,o.p=2,y)return y()}finally{m.T=v,o.p=x,o.d.f()}},oe.preconnect=function(y,v){typeof y=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,o.d.C(y,v))},oe.prefetchDNS=function(y){typeof y=="string"&&o.d.D(y)},oe.preinit=function(y,v){if(typeof y=="string"&&v&&typeof v.as=="string"){var x=v.as,A=g(x,v.crossOrigin),N=typeof v.integrity=="string"?v.integrity:void 0,k=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;x==="style"?o.d.S(y,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:A,integrity:N,fetchPriority:k}):x==="script"&&o.d.X(y,{crossOrigin:A,integrity:N,fetchPriority:k,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},oe.preinitModule=function(y,v){if(typeof y=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var x=g(v.as,v.crossOrigin);o.d.M(y,{crossOrigin:x,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&o.d.M(y)},oe.preload=function(y,v){if(typeof y=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var x=v.as,A=g(x,v.crossOrigin);o.d.L(y,x,{crossOrigin:A,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},oe.preloadModule=function(y,v){if(typeof y=="string")if(v){var x=g(v.as,v.crossOrigin);o.d.m(y,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:x,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else o.d.m(y)},oe.requestFormReset=function(y){o.d.r(y)},oe.unstable_batchedUpdates=function(y,v){return y(v)},oe.useFormState=function(y,v,x){return m.H.useFormState(y,v,x)},oe.useFormStatus=function(){return m.H.useHostTransitionStatus()},oe.version="19.1.1",oe}var am;function Qm(){if(am)return Ar.exports;am=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(u){console.error(u)}}return l(),Ar.exports=cy(),Ar.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nm;function oy(){if(nm)return di;nm=1;var l=uy(),u=Ir(),r=Qm();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function p(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function m(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function g(t){if(p(t)!==t)throw Error(o(188))}function y(t){var e=t.alternate;if(!e){if(e=p(t),e===null)throw Error(o(188));return e!==t?null:t}for(var a=t,n=e;;){var i=a.return;if(i===null)break;var c=i.alternate;if(c===null){if(n=i.return,n!==null){a=n;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===a)return g(i),t;if(c===n)return g(i),e;c=c.sibling}throw Error(o(188))}if(a.return!==n.return)a=i,n=c;else{for(var f=!1,d=i.child;d;){if(d===a){f=!0,a=i,n=c;break}if(d===n){f=!0,n=i,a=c;break}d=d.sibling}if(!f){for(d=c.child;d;){if(d===a){f=!0,a=c,n=i;break}if(d===n){f=!0,n=c,a=i;break}d=d.sibling}if(!f)throw Error(o(189))}}if(a.alternate!==n)throw Error(o(190))}if(a.tag!==3)throw Error(o(188));return a.stateNode.current===a?t:e}function v(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=v(t),e!==null)return e;t=t.sibling}return null}var x=Object.assign,A=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),k=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),U=Symbol.for("react.profiler"),W=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),Z=Symbol.for("react.context"),tt=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),ft=Symbol.for("react.suspense_list"),ot=Symbol.for("react.memo"),Tt=Symbol.for("react.lazy"),xt=Symbol.for("react.activity"),Yt=Symbol.for("react.memo_cache_sentinel"),Gt=Symbol.iterator;function Bt(t){return t===null||typeof t!="object"?null:(t=Gt&&t[Gt]||t["@@iterator"],typeof t=="function"?t:null)}var Oe=Symbol.for("react.client.reference");function Mt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Oe?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case M:return"Fragment";case U:return"Profiler";case L:return"StrictMode";case Y:return"Suspense";case ft:return"SuspenseList";case xt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case k:return"Portal";case Z:return(t.displayName||"Context")+".Provider";case J:return(t._context.displayName||"Context")+".Consumer";case tt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ot:return e=t.displayName||null,e!==null?e:Mt(t.type)||"Memo";case Tt:e=t._payload,t=t._init;try{return Mt(t(e))}catch{}}return null}var bt=Array.isArray,w=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},ut=[],b=-1;function H(t){return{current:t}}function Q(t){0>b||(t.current=ut[b],ut[b]=null,b--)}function G(t,e){b++,ut[b]=t.current,t.current=e}var F=H(null),mt=H(null),I=H(null),ie=H(null);function zt(t,e){switch(G(I,e),G(mt,t),G(F,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Td(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Td(e),t=Ad(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Q(F),G(F,t)}function Ne(){Q(F),Q(mt),Q(I)}function va(t){t.memoizedState!==null&&G(ie,t);var e=F.current,a=Ad(e,t.type);e!==a&&(G(mt,t),G(F,a))}function $e(t){mt.current===t&&(Q(F),Q(mt)),ie.current===t&&(Q(ie),ci._currentValue=P)}var Ge=Object.prototype.hasOwnProperty,gn=l.unstable_scheduleCallback,yn=l.unstable_cancelCallback,Oi=l.unstable_shouldYield,Ri=l.unstable_requestPaint,ee=l.unstable_now,dl=l.unstable_getCurrentPriorityLevel,Za=l.unstable_ImmediatePriority,Ka=l.unstable_UserBlockingPriority,xn=l.unstable_NormalPriority,uc=l.unstable_LowPriority,zi=l.unstable_IdlePriority,V=l.log,at=l.unstable_setDisableYieldValue,wt=null,ht=null;function Ht(t){if(typeof V=="function"&&at(t),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(wt,t)}catch{}}var Jt=Math.clz32?Math.clz32:vl,ha=Math.log,ml=Math.LN2;function vl(t){return t>>>=0,t===0?32:31-(ha(t)/ml|0)|0}var ga=256,We=4194304;function qe(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function ya(t,e,a){var n=t.pendingLanes;if(n===0)return 0;var i=0,c=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var d=n&134217727;return d!==0?(n=d&~c,n!==0?i=qe(n):(f&=d,f!==0?i=qe(f):a||(a=d&~t,a!==0&&(i=qe(a))))):(d=n&~c,d!==0?i=qe(d):f!==0?i=qe(f):a||(a=n&~t,a!==0&&(i=qe(a)))),i===0?0:e!==0&&e!==i&&(e&c)===0&&(c=i&-i,a=e&-e,c>=a||c===32&&(a&4194048)!==0)?e:i}function Pe(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function hl(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _i(){var t=ga;return ga<<=1,(ga&4194048)===0&&(ga=256),t}function us(){var t=We;return We<<=1,(We&62914560)===0&&(We=4194304),t}function cc(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function gl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Gv(t,e,a,n,i,c){var f=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var d=t.entanglements,h=t.expirationTimes,O=t.hiddenUpdates;for(a=f&~a;0<a;){var j=31-Jt(a),q=1<<j;d[j]=0,h[j]=-1;var R=O[j];if(R!==null)for(O[j]=null,j=0;j<R.length;j++){var z=R[j];z!==null&&(z.lane&=-536870913)}a&=~q}n!==0&&cs(t,n,0),c!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=c&~(f&~e))}function cs(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-Jt(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|a&4194090}function os(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var n=31-Jt(a),i=1<<n;i&e|t[n]&e&&(t[n]|=e),a&=~i}}function oc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function rc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function rs(){var t=X.p;return t!==0?t:(t=window.event,t===void 0?32:Xd(t.type))}function Xv(t,e){var a=X.p;try{return X.p=t,e()}finally{X.p=a}}var xa=Math.random().toString(36).slice(2),ue="__reactFiber$"+xa,fe="__reactProps$"+xa,bn="__reactContainer$"+xa,sc="__reactEvents$"+xa,Qv="__reactListeners$"+xa,Vv="__reactHandles$"+xa,ss="__reactResources$"+xa,yl="__reactMarker$"+xa;function fc(t){delete t[ue],delete t[fe],delete t[sc],delete t[Qv],delete t[Vv]}function Sn(t){var e=t[ue];if(e)return e;for(var a=t.parentNode;a;){if(e=a[bn]||a[ue]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=_d(t);t!==null;){if(a=t[ue])return a;t=_d(t)}return e}t=a,a=t.parentNode}return null}function En(t){if(t=t[ue]||t[bn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function xl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function Tn(t){var e=t[ss];return e||(e=t[ss]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Wt(t){t[yl]=!0}var fs=new Set,ps={};function Ja(t,e){An(t,e),An(t+"Capture",e)}function An(t,e){for(ps[t]=e,t=0;t<e.length;t++)fs.add(e[t])}var Zv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ds={},ms={};function Kv(t){return Ge.call(ms,t)?!0:Ge.call(ds,t)?!1:Zv.test(t)?ms[t]=!0:(ds[t]=!0,!1)}function Di(t,e,a){if(Kv(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function wi(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function Ie(t,e,a,n){if(n===null)t.removeAttribute(a);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+n)}}var pc,vs;function On(t){if(pc===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);pc=e&&e[1]||"",vs=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+pc+t+vs}var dc=!1;function mc(t,e){if(!t||dc)return"";dc=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(z){var R=z}Reflect.construct(t,[],q)}else{try{q.call()}catch(z){R=z}t.call(q.prototype)}}else{try{throw Error()}catch(z){R=z}(q=t())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(z){if(z&&R&&typeof z.stack=="string")return[z.stack,R.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=n.DetermineComponentFrameRoot(),f=c[0],d=c[1];if(f&&d){var h=f.split(`
`),O=d.split(`
`);for(i=n=0;n<h.length&&!h[n].includes("DetermineComponentFrameRoot");)n++;for(;i<O.length&&!O[i].includes("DetermineComponentFrameRoot");)i++;if(n===h.length||i===O.length)for(n=h.length-1,i=O.length-1;1<=n&&0<=i&&h[n]!==O[i];)i--;for(;1<=n&&0<=i;n--,i--)if(h[n]!==O[i]){if(n!==1||i!==1)do if(n--,i--,0>i||h[n]!==O[i]){var j=`
`+h[n].replace(" at new "," at ");return t.displayName&&j.includes("<anonymous>")&&(j=j.replace("<anonymous>",t.displayName)),j}while(1<=n&&0<=i);break}}}finally{dc=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?On(a):""}function Jv(t){switch(t.tag){case 26:case 27:case 5:return On(t.type);case 16:return On("Lazy");case 13:return On("Suspense");case 19:return On("SuspenseList");case 0:case 15:return mc(t.type,!1);case 11:return mc(t.type.render,!1);case 1:return mc(t.type,!0);case 31:return On("Activity");default:return""}}function hs(t){try{var e="";do e+=Jv(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Re(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function gs(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Fv(t){var e=gs(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,c=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(f){n=""+f,c.call(this,f)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return n},setValue:function(f){n=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ji(t){t._valueTracker||(t._valueTracker=Fv(t))}function ys(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),n="";return t&&(n=gs(t)?t.checked?"true":"false":t.value),t=n,t!==a?(e.setValue(t),!0):!1}function Ui(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var $v=/[\n"\\]/g;function ze(t){return t.replace($v,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vc(t,e,a,n,i,c,f,d){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Re(e)):t.value!==""+Re(e)&&(t.value=""+Re(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?hc(t,f,Re(e)):a!=null?hc(t,f,Re(a)):n!=null&&t.removeAttribute("value"),i==null&&c!=null&&(t.defaultChecked=!!c),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Re(d):t.removeAttribute("name")}function xs(t,e,a,n,i,c,f,d){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;a=a!=null?""+Re(a):"",e=e!=null?""+Re(e):a,d||e===t.value||(t.value=e),t.defaultValue=e}n=n??i,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=d?t.checked:!!n,t.defaultChecked=!!n,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function hc(t,e,a){e==="number"&&Ui(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Rn(t,e,a,n){if(t=t.options,e){e={};for(var i=0;i<a.length;i++)e["$"+a[i]]=!0;for(a=0;a<t.length;a++)i=e.hasOwnProperty("$"+t[a].value),t[a].selected!==i&&(t[a].selected=i),i&&n&&(t[a].defaultSelected=!0)}else{for(a=""+Re(a),e=null,i=0;i<t.length;i++){if(t[i].value===a){t[i].selected=!0,n&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function bs(t,e,a){if(e!=null&&(e=""+Re(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+Re(a):""}function Ss(t,e,a,n){if(e==null){if(n!=null){if(a!=null)throw Error(o(92));if(bt(n)){if(1<n.length)throw Error(o(93));n=n[0]}a=n}a==null&&(a=""),e=a}a=Re(e),t.defaultValue=a,n=t.textContent,n===a&&n!==""&&n!==null&&(t.value=n)}function zn(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var Wv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Es(t,e,a){var n=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,a):typeof a!="number"||a===0||Wv.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function Ts(t,e,a){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,a!=null){for(var n in a)!a.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var i in e)n=e[i],e.hasOwnProperty(i)&&a[i]!==n&&Es(t,i,n)}else for(var c in e)e.hasOwnProperty(c)&&Es(t,c,e[c])}function gc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Iv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Mi(t){return Iv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var yc=null;function xc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var _n=null,Dn=null;function As(t){var e=En(t);if(e&&(t=e.stateNode)){var a=t[fe]||null;t:switch(t=e.stateNode,e.type){case"input":if(vc(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+ze(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var n=a[e];if(n!==t&&n.form===t.form){var i=n[fe]||null;if(!i)throw Error(o(90));vc(n,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<a.length;e++)n=a[e],n.form===t.form&&ys(n)}break t;case"textarea":bs(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Rn(t,!!a.multiple,e,!1)}}}var bc=!1;function Os(t,e,a){if(bc)return t(e,a);bc=!0;try{var n=t(e);return n}finally{if(bc=!1,(_n!==null||Dn!==null)&&(yu(),_n&&(e=_n,t=Dn,Dn=_n=null,As(e),t)))for(e=0;e<t.length;e++)As(t[e])}}function bl(t,e){var a=t.stateNode;if(a===null)return null;var n=a[fe]||null;if(n===null)return null;a=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(o(231,e,typeof a));return a}var ta=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sc=!1;if(ta)try{var Sl={};Object.defineProperty(Sl,"passive",{get:function(){Sc=!0}}),window.addEventListener("test",Sl,Sl),window.removeEventListener("test",Sl,Sl)}catch{Sc=!1}var ba=null,Ec=null,Ci=null;function Rs(){if(Ci)return Ci;var t,e=Ec,a=e.length,n,i="value"in ba?ba.value:ba.textContent,c=i.length;for(t=0;t<a&&e[t]===i[t];t++);var f=a-t;for(n=1;n<=f&&e[a-n]===i[c-n];n++);return Ci=i.slice(t,1<n?1-n:void 0)}function Ni(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function qi(){return!0}function zs(){return!1}function pe(t){function e(a,n,i,c,f){this._reactName=a,this._targetInst=i,this.type=n,this.nativeEvent=c,this.target=f,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(a=t[d],this[d]=a?a(c):c[d]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?qi:zs,this.isPropagationStopped=zs,this}return x(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=qi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=qi)},persist:function(){},isPersistent:qi}),e}var Fa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bi=pe(Fa),El=x({},Fa,{view:0,detail:0}),th=pe(El),Tc,Ac,Tl,Hi=x({},El,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Rc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Tl&&(Tl&&t.type==="mousemove"?(Tc=t.screenX-Tl.screenX,Ac=t.screenY-Tl.screenY):Ac=Tc=0,Tl=t),Tc)},movementY:function(t){return"movementY"in t?t.movementY:Ac}}),_s=pe(Hi),eh=x({},Hi,{dataTransfer:0}),ah=pe(eh),nh=x({},El,{relatedTarget:0}),Oc=pe(nh),lh=x({},Fa,{animationName:0,elapsedTime:0,pseudoElement:0}),ih=pe(lh),uh=x({},Fa,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),ch=pe(uh),oh=x({},Fa,{data:0}),Ds=pe(oh),rh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},sh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ph(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=fh[t])?!!e[t]:!1}function Rc(){return ph}var dh=x({},El,{key:function(t){if(t.key){var e=rh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ni(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?sh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Rc,charCode:function(t){return t.type==="keypress"?Ni(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ni(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),mh=pe(dh),vh=x({},Hi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ws=pe(vh),hh=x({},El,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Rc}),gh=pe(hh),yh=x({},Fa,{propertyName:0,elapsedTime:0,pseudoElement:0}),xh=pe(yh),bh=x({},Hi,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Sh=pe(bh),Eh=x({},Fa,{newState:0,oldState:0}),Th=pe(Eh),Ah=[9,13,27,32],zc=ta&&"CompositionEvent"in window,Al=null;ta&&"documentMode"in document&&(Al=document.documentMode);var Oh=ta&&"TextEvent"in window&&!Al,js=ta&&(!zc||Al&&8<Al&&11>=Al),Us=" ",Ms=!1;function Cs(t,e){switch(t){case"keyup":return Ah.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ns(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var wn=!1;function Rh(t,e){switch(t){case"compositionend":return Ns(e);case"keypress":return e.which!==32?null:(Ms=!0,Us);case"textInput":return t=e.data,t===Us&&Ms?null:t;default:return null}}function zh(t,e){if(wn)return t==="compositionend"||!zc&&Cs(t,e)?(t=Rs(),Ci=Ec=ba=null,wn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return js&&e.locale!=="ko"?null:e.data;default:return null}}var _h={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qs(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!_h[t.type]:e==="textarea"}function Bs(t,e,a,n){_n?Dn?Dn.push(n):Dn=[n]:_n=n,e=Au(e,"onChange"),0<e.length&&(a=new Bi("onChange","change",null,a,n),t.push({event:a,listeners:e}))}var Ol=null,Rl=null;function Dh(t){yd(t,0)}function Li(t){var e=xl(t);if(ys(e))return t}function Hs(t,e){if(t==="change")return e}var Ls=!1;if(ta){var _c;if(ta){var Dc="oninput"in document;if(!Dc){var ks=document.createElement("div");ks.setAttribute("oninput","return;"),Dc=typeof ks.oninput=="function"}_c=Dc}else _c=!1;Ls=_c&&(!document.documentMode||9<document.documentMode)}function Ys(){Ol&&(Ol.detachEvent("onpropertychange",Gs),Rl=Ol=null)}function Gs(t){if(t.propertyName==="value"&&Li(Rl)){var e=[];Bs(e,Rl,t,xc(t)),Os(Dh,e)}}function wh(t,e,a){t==="focusin"?(Ys(),Ol=e,Rl=a,Ol.attachEvent("onpropertychange",Gs)):t==="focusout"&&Ys()}function jh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Li(Rl)}function Uh(t,e){if(t==="click")return Li(e)}function Mh(t,e){if(t==="input"||t==="change")return Li(e)}function Ch(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ye=typeof Object.is=="function"?Object.is:Ch;function zl(t,e){if(ye(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),n=Object.keys(e);if(a.length!==n.length)return!1;for(n=0;n<a.length;n++){var i=a[n];if(!Ge.call(e,i)||!ye(t[i],e[i]))return!1}return!0}function Xs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Qs(t,e){var a=Xs(t);t=0;for(var n;a;){if(a.nodeType===3){if(n=t+a.textContent.length,t<=e&&n>=e)return{node:a,offset:e-t};t=n}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=Xs(a)}}function Vs(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Vs(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Zs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ui(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=Ui(t.document)}return e}function wc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Nh=ta&&"documentMode"in document&&11>=document.documentMode,jn=null,jc=null,_l=null,Uc=!1;function Ks(t,e,a){var n=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Uc||jn==null||jn!==Ui(n)||(n=jn,"selectionStart"in n&&wc(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),_l&&zl(_l,n)||(_l=n,n=Au(jc,"onSelect"),0<n.length&&(e=new Bi("onSelect","select",null,e,a),t.push({event:e,listeners:n}),e.target=jn)))}function $a(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Un={animationend:$a("Animation","AnimationEnd"),animationiteration:$a("Animation","AnimationIteration"),animationstart:$a("Animation","AnimationStart"),transitionrun:$a("Transition","TransitionRun"),transitionstart:$a("Transition","TransitionStart"),transitioncancel:$a("Transition","TransitionCancel"),transitionend:$a("Transition","TransitionEnd")},Mc={},Js={};ta&&(Js=document.createElement("div").style,"AnimationEvent"in window||(delete Un.animationend.animation,delete Un.animationiteration.animation,delete Un.animationstart.animation),"TransitionEvent"in window||delete Un.transitionend.transition);function Wa(t){if(Mc[t])return Mc[t];if(!Un[t])return t;var e=Un[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in Js)return Mc[t]=e[a];return t}var Fs=Wa("animationend"),$s=Wa("animationiteration"),Ws=Wa("animationstart"),qh=Wa("transitionrun"),Bh=Wa("transitionstart"),Hh=Wa("transitioncancel"),Ps=Wa("transitionend"),Is=new Map,Cc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cc.push("scrollEnd");function Be(t,e){Is.set(t,e),Ja(e,[t])}var tf=new WeakMap;function _e(t,e){if(typeof t=="object"&&t!==null){var a=tf.get(t);return a!==void 0?a:(e={value:t,source:e,stack:hs(e)},tf.set(t,e),e)}return{value:t,source:e,stack:hs(e)}}var De=[],Mn=0,Nc=0;function ki(){for(var t=Mn,e=Nc=Mn=0;e<t;){var a=De[e];De[e++]=null;var n=De[e];De[e++]=null;var i=De[e];De[e++]=null;var c=De[e];if(De[e++]=null,n!==null&&i!==null){var f=n.pending;f===null?i.next=i:(i.next=f.next,f.next=i),n.pending=i}c!==0&&ef(a,i,c)}}function Yi(t,e,a,n){De[Mn++]=t,De[Mn++]=e,De[Mn++]=a,De[Mn++]=n,Nc|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function qc(t,e,a,n){return Yi(t,e,a,n),Gi(t)}function Cn(t,e){return Yi(t,null,null,e),Gi(t)}function ef(t,e,a){t.lanes|=a;var n=t.alternate;n!==null&&(n.lanes|=a);for(var i=!1,c=t.return;c!==null;)c.childLanes|=a,n=c.alternate,n!==null&&(n.childLanes|=a),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(i=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,i&&e!==null&&(i=31-Jt(a),t=c.hiddenUpdates,n=t[i],n===null?t[i]=[e]:n.push(e),e.lane=a|536870912),c):null}function Gi(t){if(50<Il)throw Il=0,Xo=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Nn={};function Lh(t,e,a,n){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function xe(t,e,a,n){return new Lh(t,e,a,n)}function Bc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ea(t,e){var a=t.alternate;return a===null?(a=xe(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function af(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xi(t,e,a,n,i,c){var f=0;if(n=t,typeof t=="function")Bc(t)&&(f=1);else if(typeof t=="string")f=Yg(t,a,F.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case xt:return t=xe(31,a,e,i),t.elementType=xt,t.lanes=c,t;case M:return Pa(a.children,i,c,e);case L:f=8,i|=24;break;case U:return t=xe(12,a,e,i|2),t.elementType=U,t.lanes=c,t;case Y:return t=xe(13,a,e,i),t.elementType=Y,t.lanes=c,t;case ft:return t=xe(19,a,e,i),t.elementType=ft,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case W:case Z:f=10;break t;case J:f=9;break t;case tt:f=11;break t;case ot:f=14;break t;case Tt:f=16,n=null;break t}f=29,a=Error(o(130,t===null?"null":typeof t,"")),n=null}return e=xe(f,a,e,i),e.elementType=t,e.type=n,e.lanes=c,e}function Pa(t,e,a,n){return t=xe(7,t,n,e),t.lanes=a,t}function Hc(t,e,a){return t=xe(6,t,null,e),t.lanes=a,t}function Lc(t,e,a){return e=xe(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var qn=[],Bn=0,Qi=null,Vi=0,we=[],je=0,Ia=null,aa=1,na="";function tn(t,e){qn[Bn++]=Vi,qn[Bn++]=Qi,Qi=t,Vi=e}function nf(t,e,a){we[je++]=aa,we[je++]=na,we[je++]=Ia,Ia=t;var n=aa;t=na;var i=32-Jt(n)-1;n&=~(1<<i),a+=1;var c=32-Jt(e)+i;if(30<c){var f=i-i%5;c=(n&(1<<f)-1).toString(32),n>>=f,i-=f,aa=1<<32-Jt(e)+i|a<<i|n,na=c+t}else aa=1<<c|a<<i|n,na=t}function kc(t){t.return!==null&&(tn(t,1),nf(t,1,0))}function Yc(t){for(;t===Qi;)Qi=qn[--Bn],qn[Bn]=null,Vi=qn[--Bn],qn[Bn]=null;for(;t===Ia;)Ia=we[--je],we[je]=null,na=we[--je],we[je]=null,aa=we[--je],we[je]=null}var se=null,Lt=null,yt=!1,en=null,Xe=!1,Gc=Error(o(519));function an(t){var e=Error(o(418,""));throw jl(_e(e,t)),Gc}function lf(t){var e=t.stateNode,a=t.type,n=t.memoizedProps;switch(e[ue]=t,e[fe]=n,a){case"dialog":dt("cancel",e),dt("close",e);break;case"iframe":case"object":case"embed":dt("load",e);break;case"video":case"audio":for(a=0;a<ei.length;a++)dt(ei[a],e);break;case"source":dt("error",e);break;case"img":case"image":case"link":dt("error",e),dt("load",e);break;case"details":dt("toggle",e);break;case"input":dt("invalid",e),xs(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),ji(e);break;case"select":dt("invalid",e);break;case"textarea":dt("invalid",e),Ss(e,n.value,n.defaultValue,n.children),ji(e)}a=n.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||n.suppressHydrationWarning===!0||Ed(e.textContent,a)?(n.popover!=null&&(dt("beforetoggle",e),dt("toggle",e)),n.onScroll!=null&&dt("scroll",e),n.onScrollEnd!=null&&dt("scrollend",e),n.onClick!=null&&(e.onclick=Ou),e=!0):e=!1,e||an(t)}function uf(t){for(se=t.return;se;)switch(se.tag){case 5:case 13:Xe=!1;return;case 27:case 3:Xe=!0;return;default:se=se.return}}function Dl(t){if(t!==se)return!1;if(!yt)return uf(t),yt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||ir(t.type,t.memoizedProps)),a=!a),a&&Lt&&an(t),uf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){Lt=Le(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}Lt=null}}else e===27?(e=Lt,qa(t.type)?(t=rr,rr=null,Lt=t):Lt=e):Lt=se?Le(t.stateNode.nextSibling):null;return!0}function wl(){Lt=se=null,yt=!1}function cf(){var t=en;return t!==null&&(ve===null?ve=t:ve.push.apply(ve,t),en=null),t}function jl(t){en===null?en=[t]:en.push(t)}var Xc=H(null),nn=null,la=null;function Sa(t,e,a){G(Xc,e._currentValue),e._currentValue=a}function ia(t){t._currentValue=Xc.current,Q(Xc)}function Qc(t,e,a){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===a)break;t=t.return}}function Vc(t,e,a,n){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var c=i.dependencies;if(c!==null){var f=i.child;c=c.firstContext;t:for(;c!==null;){var d=c;c=i;for(var h=0;h<e.length;h++)if(d.context===e[h]){c.lanes|=a,d=c.alternate,d!==null&&(d.lanes|=a),Qc(c.return,a,t),n||(f=null);break t}c=d.next}}else if(i.tag===18){if(f=i.return,f===null)throw Error(o(341));f.lanes|=a,c=f.alternate,c!==null&&(c.lanes|=a),Qc(f,a,t),f=null}else f=i.child;if(f!==null)f.return=i;else for(f=i;f!==null;){if(f===t){f=null;break}if(i=f.sibling,i!==null){i.return=f.return,f=i;break}f=f.return}i=f}}function Ul(t,e,a,n){t=null;for(var i=e,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var f=i.alternate;if(f===null)throw Error(o(387));if(f=f.memoizedProps,f!==null){var d=i.type;ye(i.pendingProps.value,f.value)||(t!==null?t.push(d):t=[d])}}else if(i===ie.current){if(f=i.alternate,f===null)throw Error(o(387));f.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(ci):t=[ci])}i=i.return}t!==null&&Vc(e,t,a,n),e.flags|=262144}function Zi(t){for(t=t.firstContext;t!==null;){if(!ye(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ln(t){nn=t,la=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ce(t){return of(nn,t)}function Ki(t,e){return nn===null&&ln(t),of(t,e)}function of(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},la===null){if(t===null)throw Error(o(308));la=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else la=la.next=e;return a}var kh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},Yh=l.unstable_scheduleCallback,Gh=l.unstable_NormalPriority,Ft={$$typeof:Z,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zc(){return{controller:new kh,data:new Map,refCount:0}}function Ml(t){t.refCount--,t.refCount===0&&Yh(Gh,function(){t.controller.abort()})}var Cl=null,Kc=0,Hn=0,Ln=null;function Xh(t,e){if(Cl===null){var a=Cl=[];Kc=0,Hn=$o(),Ln={status:"pending",value:void 0,then:function(n){a.push(n)}}}return Kc++,e.then(rf,rf),e}function rf(){if(--Kc===0&&Cl!==null){Ln!==null&&(Ln.status="fulfilled");var t=Cl;Cl=null,Hn=0,Ln=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Qh(t,e){var a=[],n={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var i=0;i<a.length;i++)(0,a[i])(e)},function(i){for(n.status="rejected",n.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),n}var sf=w.S;w.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Xh(t,e),sf!==null&&sf(t,e)};var un=H(null);function Jc(){var t=un.current;return t!==null?t:jt.pooledCache}function Ji(t,e){e===null?G(un,un.current):G(un,e.pool)}function ff(){var t=Jc();return t===null?null:{parent:Ft._currentValue,pool:t}}var Nl=Error(o(460)),pf=Error(o(474)),Fi=Error(o(542)),Fc={then:function(){}};function df(t){return t=t.status,t==="fulfilled"||t==="rejected"}function $i(){}function mf(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then($i,$i),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,hf(t),t;default:if(typeof e.status=="string")e.then($i,$i);else{if(t=jt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=n}},function(n){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,hf(t),t}throw ql=e,Nl}}var ql=null;function vf(){if(ql===null)throw Error(o(459));var t=ql;return ql=null,t}function hf(t){if(t===Nl||t===Fi)throw Error(o(483))}var Ea=!1;function $c(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Wc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ta(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Aa(t,e,a){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(St&2)!==0){var i=n.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),n.pending=e,e=Gi(t),ef(t,null,a),e}return Yi(t,n,e,a),Gi(t)}function Bl(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,a|=n,e.lanes=a,os(t,a)}}function Pc(t,e){var a=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,a===n)){var i=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?i=c=f:c=c.next=f,a=a.next}while(a!==null);c===null?i=c=e:c=c.next=e}else i=c=e;a={baseState:n.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:n.shared,callbacks:n.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var Ic=!1;function Hl(){if(Ic){var t=Ln;if(t!==null)throw t}}function Ll(t,e,a,n){Ic=!1;var i=t.updateQueue;Ea=!1;var c=i.firstBaseUpdate,f=i.lastBaseUpdate,d=i.shared.pending;if(d!==null){i.shared.pending=null;var h=d,O=h.next;h.next=null,f===null?c=O:f.next=O,f=h;var j=t.alternate;j!==null&&(j=j.updateQueue,d=j.lastBaseUpdate,d!==f&&(d===null?j.firstBaseUpdate=O:d.next=O,j.lastBaseUpdate=h))}if(c!==null){var q=i.baseState;f=0,j=O=h=null,d=c;do{var R=d.lane&-536870913,z=R!==d.lane;if(z?(vt&R)===R:(n&R)===R){R!==0&&R===Hn&&(Ic=!0),j!==null&&(j=j.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var lt=t,et=d;R=e;var Rt=a;switch(et.tag){case 1:if(lt=et.payload,typeof lt=="function"){q=lt.call(Rt,q,R);break t}q=lt;break t;case 3:lt.flags=lt.flags&-65537|128;case 0:if(lt=et.payload,R=typeof lt=="function"?lt.call(Rt,q,R):lt,R==null)break t;q=x({},q,R);break t;case 2:Ea=!0}}R=d.callback,R!==null&&(t.flags|=64,z&&(t.flags|=8192),z=i.callbacks,z===null?i.callbacks=[R]:z.push(R))}else z={lane:R,tag:d.tag,payload:d.payload,callback:d.callback,next:null},j===null?(O=j=z,h=q):j=j.next=z,f|=R;if(d=d.next,d===null){if(d=i.shared.pending,d===null)break;z=d,d=z.next,z.next=null,i.lastBaseUpdate=z,i.shared.pending=null}}while(!0);j===null&&(h=q),i.baseState=h,i.firstBaseUpdate=O,i.lastBaseUpdate=j,c===null&&(i.shared.lanes=0),Ua|=f,t.lanes=f,t.memoizedState=q}}function gf(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function yf(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)gf(a[t],e)}var kn=H(null),Wi=H(0);function xf(t,e){t=pa,G(Wi,t),G(kn,e),pa=t|e.baseLanes}function to(){G(Wi,pa),G(kn,kn.current)}function eo(){pa=Wi.current,Q(kn),Q(Wi)}var Oa=0,rt=null,At=null,Zt=null,Pi=!1,Yn=!1,cn=!1,Ii=0,kl=0,Gn=null,Vh=0;function Xt(){throw Error(o(321))}function ao(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!ye(t[a],e[a]))return!1;return!0}function no(t,e,a,n,i,c){return Oa=c,rt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,w.H=t===null||t.memoizedState===null?ap:np,cn=!1,c=a(n,i),cn=!1,Yn&&(c=Sf(e,a,n,i)),bf(t),c}function bf(t){w.H=iu;var e=At!==null&&At.next!==null;if(Oa=0,Zt=At=rt=null,Pi=!1,kl=0,Gn=null,e)throw Error(o(300));t===null||Pt||(t=t.dependencies,t!==null&&Zi(t)&&(Pt=!0))}function Sf(t,e,a,n){rt=t;var i=0;do{if(Yn&&(Gn=null),kl=0,Yn=!1,25<=i)throw Error(o(301));if(i+=1,Zt=At=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}w.H=Ph,c=e(a,n)}while(Yn);return c}function Zh(){var t=w.H,e=t.useState()[0];return e=typeof e.then=="function"?Yl(e):e,t=t.useState()[0],(At!==null?At.memoizedState:null)!==t&&(rt.flags|=1024),e}function lo(){var t=Ii!==0;return Ii=0,t}function io(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function uo(t){if(Pi){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Pi=!1}Oa=0,Zt=At=rt=null,Yn=!1,kl=Ii=0,Gn=null}function de(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Zt===null?rt.memoizedState=Zt=t:Zt=Zt.next=t,Zt}function Kt(){if(At===null){var t=rt.alternate;t=t!==null?t.memoizedState:null}else t=At.next;var e=Zt===null?rt.memoizedState:Zt.next;if(e!==null)Zt=e,At=t;else{if(t===null)throw rt.alternate===null?Error(o(467)):Error(o(310));At=t,t={memoizedState:At.memoizedState,baseState:At.baseState,baseQueue:At.baseQueue,queue:At.queue,next:null},Zt===null?rt.memoizedState=Zt=t:Zt=Zt.next=t}return Zt}function co(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Yl(t){var e=kl;return kl+=1,Gn===null&&(Gn=[]),t=mf(Gn,t,e),e=rt,(Zt===null?e.memoizedState:Zt.next)===null&&(e=e.alternate,w.H=e===null||e.memoizedState===null?ap:np),t}function tu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Yl(t);if(t.$$typeof===Z)return ce(t)}throw Error(o(438,String(t)))}function oo(t){var e=null,a=rt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var n=rt.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=co(),rt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),n=0;n<t;n++)a[n]=Yt;return e.index++,a}function ua(t,e){return typeof e=="function"?e(t):e}function eu(t){var e=Kt();return ro(e,At,t)}function ro(t,e,a){var n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=a;var i=t.baseQueue,c=n.pending;if(c!==null){if(i!==null){var f=i.next;i.next=c.next,c.next=f}e.baseQueue=i=c,n.pending=null}if(c=t.baseState,i===null)t.memoizedState=c;else{e=i.next;var d=f=null,h=null,O=e,j=!1;do{var q=O.lane&-536870913;if(q!==O.lane?(vt&q)===q:(Oa&q)===q){var R=O.revertLane;if(R===0)h!==null&&(h=h.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),q===Hn&&(j=!0);else if((Oa&R)===R){O=O.next,R===Hn&&(j=!0);continue}else q={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},h===null?(d=h=q,f=c):h=h.next=q,rt.lanes|=R,Ua|=R;q=O.action,cn&&a(c,q),c=O.hasEagerState?O.eagerState:a(c,q)}else R={lane:q,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},h===null?(d=h=R,f=c):h=h.next=R,rt.lanes|=q,Ua|=q;O=O.next}while(O!==null&&O!==e);if(h===null?f=c:h.next=d,!ye(c,t.memoizedState)&&(Pt=!0,j&&(a=Ln,a!==null)))throw a;t.memoizedState=c,t.baseState=f,t.baseQueue=h,n.lastRenderedState=c}return i===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function so(t){var e=Kt(),a=e.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=t;var n=a.dispatch,i=a.pending,c=e.memoizedState;if(i!==null){a.pending=null;var f=i=i.next;do c=t(c,f.action),f=f.next;while(f!==i);ye(c,e.memoizedState)||(Pt=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),a.lastRenderedState=c}return[c,n]}function Ef(t,e,a){var n=rt,i=Kt(),c=yt;if(c){if(a===void 0)throw Error(o(407));a=a()}else a=e();var f=!ye((At||i).memoizedState,a);f&&(i.memoizedState=a,Pt=!0),i=i.queue;var d=Of.bind(null,n,i,t);if(Gl(2048,8,d,[t]),i.getSnapshot!==e||f||Zt!==null&&Zt.memoizedState.tag&1){if(n.flags|=2048,Xn(9,au(),Af.bind(null,n,i,a,e),null),jt===null)throw Error(o(349));c||(Oa&124)!==0||Tf(n,e,a)}return a}function Tf(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=rt.updateQueue,e===null?(e=co(),rt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function Af(t,e,a,n){e.value=a,e.getSnapshot=n,Rf(e)&&zf(t)}function Of(t,e,a){return a(function(){Rf(e)&&zf(t)})}function Rf(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!ye(t,a)}catch{return!0}}function zf(t){var e=Cn(t,2);e!==null&&Ae(e,t,2)}function fo(t){var e=de();if(typeof t=="function"){var a=t;if(t=a(),cn){Ht(!0);try{a()}finally{Ht(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ua,lastRenderedState:t},e}function _f(t,e,a,n){return t.baseState=a,ro(t,At,typeof n=="function"?n:ua)}function Kh(t,e,a,n,i){if(lu(t))throw Error(o(485));if(t=e.action,t!==null){var c={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){c.listeners.push(f)}};w.T!==null?a(!0):c.isTransition=!1,n(c),a=e.pending,a===null?(c.next=e.pending=c,Df(e,c)):(c.next=a.next,e.pending=a.next=c)}}function Df(t,e){var a=e.action,n=e.payload,i=t.state;if(e.isTransition){var c=w.T,f={};w.T=f;try{var d=a(i,n),h=w.S;h!==null&&h(f,d),wf(t,e,d)}catch(O){po(t,e,O)}finally{w.T=c}}else try{c=a(i,n),wf(t,e,c)}catch(O){po(t,e,O)}}function wf(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(n){jf(t,e,n)},function(n){return po(t,e,n)}):jf(t,e,a)}function jf(t,e,a){e.status="fulfilled",e.value=a,Uf(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,Df(t,a)))}function po(t,e,a){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=a,Uf(e),e=e.next;while(e!==n)}t.action=null}function Uf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Mf(t,e){return e}function Cf(t,e){if(yt){var a=jt.formState;if(a!==null){t:{var n=rt;if(yt){if(Lt){e:{for(var i=Lt,c=Xe;i.nodeType!==8;){if(!c){i=null;break e}if(i=Le(i.nextSibling),i===null){i=null;break e}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Lt=Le(i.nextSibling),n=i.data==="F!";break t}}an(n)}n=!1}n&&(e=a[0])}}return a=de(),a.memoizedState=a.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Mf,lastRenderedState:e},a.queue=n,a=If.bind(null,rt,n),n.dispatch=a,n=fo(!1),c=yo.bind(null,rt,!1,n.queue),n=de(),i={state:e,dispatch:null,action:t,pending:null},n.queue=i,a=Kh.bind(null,rt,i,c,a),i.dispatch=a,n.memoizedState=t,[e,a,!1]}function Nf(t){var e=Kt();return qf(e,At,t)}function qf(t,e,a){if(e=ro(t,e,Mf)[0],t=eu(ua)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=Yl(e)}catch(f){throw f===Nl?Fi:f}else n=e;e=Kt();var i=e.queue,c=i.dispatch;return a!==e.memoizedState&&(rt.flags|=2048,Xn(9,au(),Jh.bind(null,i,a),null)),[n,c,t]}function Jh(t,e){t.action=e}function Bf(t){var e=Kt(),a=At;if(a!==null)return qf(e,a,t);Kt(),e=e.memoizedState,a=Kt();var n=a.queue.dispatch;return a.memoizedState=t,[e,n,!1]}function Xn(t,e,a,n){return t={tag:t,create:a,deps:n,inst:e,next:null},e=rt.updateQueue,e===null&&(e=co(),rt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(n=a.next,a.next=t,t.next=n,e.lastEffect=t),t}function au(){return{destroy:void 0,resource:void 0}}function Hf(){return Kt().memoizedState}function nu(t,e,a,n){var i=de();n=n===void 0?null:n,rt.flags|=t,i.memoizedState=Xn(1|e,au(),a,n)}function Gl(t,e,a,n){var i=Kt();n=n===void 0?null:n;var c=i.memoizedState.inst;At!==null&&n!==null&&ao(n,At.memoizedState.deps)?i.memoizedState=Xn(e,c,a,n):(rt.flags|=t,i.memoizedState=Xn(1|e,c,a,n))}function Lf(t,e){nu(8390656,8,t,e)}function kf(t,e){Gl(2048,8,t,e)}function Yf(t,e){return Gl(4,2,t,e)}function Gf(t,e){return Gl(4,4,t,e)}function Xf(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Qf(t,e,a){a=a!=null?a.concat([t]):null,Gl(4,4,Xf.bind(null,e,t),a)}function mo(){}function Vf(t,e){var a=Kt();e=e===void 0?null:e;var n=a.memoizedState;return e!==null&&ao(e,n[1])?n[0]:(a.memoizedState=[t,e],t)}function Zf(t,e){var a=Kt();e=e===void 0?null:e;var n=a.memoizedState;if(e!==null&&ao(e,n[1]))return n[0];if(n=t(),cn){Ht(!0);try{t()}finally{Ht(!1)}}return a.memoizedState=[n,e],n}function vo(t,e,a){return a===void 0||(Oa&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=Fp(),rt.lanes|=t,Ua|=t,a)}function Kf(t,e,a,n){return ye(a,e)?a:kn.current!==null?(t=vo(t,a,n),ye(t,e)||(Pt=!0),t):(Oa&42)===0?(Pt=!0,t.memoizedState=a):(t=Fp(),rt.lanes|=t,Ua|=t,e)}function Jf(t,e,a,n,i){var c=X.p;X.p=c!==0&&8>c?c:8;var f=w.T,d={};w.T=d,yo(t,!1,e,a);try{var h=i(),O=w.S;if(O!==null&&O(d,h),h!==null&&typeof h=="object"&&typeof h.then=="function"){var j=Qh(h,n);Xl(t,e,j,Te(t))}else Xl(t,e,n,Te(t))}catch(q){Xl(t,e,{then:function(){},status:"rejected",reason:q},Te())}finally{X.p=c,w.T=f}}function Fh(){}function ho(t,e,a,n){if(t.tag!==5)throw Error(o(476));var i=Ff(t).queue;Jf(t,i,e,P,a===null?Fh:function(){return $f(t),a(n)})}function Ff(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ua,lastRenderedState:P},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ua,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function $f(t){var e=Ff(t).next.queue;Xl(t,e,{},Te())}function go(){return ce(ci)}function Wf(){return Kt().memoizedState}function Pf(){return Kt().memoizedState}function $h(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=Te();t=Ta(a);var n=Aa(e,t,a);n!==null&&(Ae(n,e,a),Bl(n,e,a)),e={cache:Zc()},t.payload=e;return}e=e.return}}function Wh(t,e,a){var n=Te();a={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},lu(t)?tp(e,a):(a=qc(t,e,a,n),a!==null&&(Ae(a,t,n),ep(a,e,n)))}function If(t,e,a){var n=Te();Xl(t,e,a,n)}function Xl(t,e,a,n){var i={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(lu(t))tp(e,i);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var f=e.lastRenderedState,d=c(f,a);if(i.hasEagerState=!0,i.eagerState=d,ye(d,f))return Yi(t,e,i,0),jt===null&&ki(),!1}catch{}finally{}if(a=qc(t,e,i,n),a!==null)return Ae(a,t,n),ep(a,e,n),!0}return!1}function yo(t,e,a,n){if(n={lane:2,revertLane:$o(),action:n,hasEagerState:!1,eagerState:null,next:null},lu(t)){if(e)throw Error(o(479))}else e=qc(t,a,n,2),e!==null&&Ae(e,t,2)}function lu(t){var e=t.alternate;return t===rt||e!==null&&e===rt}function tp(t,e){Yn=Pi=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function ep(t,e,a){if((a&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,a|=n,e.lanes=a,os(t,a)}}var iu={readContext:ce,use:tu,useCallback:Xt,useContext:Xt,useEffect:Xt,useImperativeHandle:Xt,useLayoutEffect:Xt,useInsertionEffect:Xt,useMemo:Xt,useReducer:Xt,useRef:Xt,useState:Xt,useDebugValue:Xt,useDeferredValue:Xt,useTransition:Xt,useSyncExternalStore:Xt,useId:Xt,useHostTransitionStatus:Xt,useFormState:Xt,useActionState:Xt,useOptimistic:Xt,useMemoCache:Xt,useCacheRefresh:Xt},ap={readContext:ce,use:tu,useCallback:function(t,e){return de().memoizedState=[t,e===void 0?null:e],t},useContext:ce,useEffect:Lf,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,nu(4194308,4,Xf.bind(null,e,t),a)},useLayoutEffect:function(t,e){return nu(4194308,4,t,e)},useInsertionEffect:function(t,e){nu(4,2,t,e)},useMemo:function(t,e){var a=de();e=e===void 0?null:e;var n=t();if(cn){Ht(!0);try{t()}finally{Ht(!1)}}return a.memoizedState=[n,e],n},useReducer:function(t,e,a){var n=de();if(a!==void 0){var i=a(e);if(cn){Ht(!0);try{a(e)}finally{Ht(!1)}}}else i=e;return n.memoizedState=n.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},n.queue=t,t=t.dispatch=Wh.bind(null,rt,t),[n.memoizedState,t]},useRef:function(t){var e=de();return t={current:t},e.memoizedState=t},useState:function(t){t=fo(t);var e=t.queue,a=If.bind(null,rt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:mo,useDeferredValue:function(t,e){var a=de();return vo(a,t,e)},useTransition:function(){var t=fo(!1);return t=Jf.bind(null,rt,t.queue,!0,!1),de().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var n=rt,i=de();if(yt){if(a===void 0)throw Error(o(407));a=a()}else{if(a=e(),jt===null)throw Error(o(349));(vt&124)!==0||Tf(n,e,a)}i.memoizedState=a;var c={value:a,getSnapshot:e};return i.queue=c,Lf(Of.bind(null,n,c,t),[t]),n.flags|=2048,Xn(9,au(),Af.bind(null,n,c,a,e),null),a},useId:function(){var t=de(),e=jt.identifierPrefix;if(yt){var a=na,n=aa;a=(n&~(1<<32-Jt(n)-1)).toString(32)+a,e="«"+e+"R"+a,a=Ii++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=Vh++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:go,useFormState:Cf,useActionState:Cf,useOptimistic:function(t){var e=de();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=yo.bind(null,rt,!0,a),a.dispatch=e,[t,e]},useMemoCache:oo,useCacheRefresh:function(){return de().memoizedState=$h.bind(null,rt)}},np={readContext:ce,use:tu,useCallback:Vf,useContext:ce,useEffect:kf,useImperativeHandle:Qf,useInsertionEffect:Yf,useLayoutEffect:Gf,useMemo:Zf,useReducer:eu,useRef:Hf,useState:function(){return eu(ua)},useDebugValue:mo,useDeferredValue:function(t,e){var a=Kt();return Kf(a,At.memoizedState,t,e)},useTransition:function(){var t=eu(ua)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Yl(t),e]},useSyncExternalStore:Ef,useId:Wf,useHostTransitionStatus:go,useFormState:Nf,useActionState:Nf,useOptimistic:function(t,e){var a=Kt();return _f(a,At,t,e)},useMemoCache:oo,useCacheRefresh:Pf},Ph={readContext:ce,use:tu,useCallback:Vf,useContext:ce,useEffect:kf,useImperativeHandle:Qf,useInsertionEffect:Yf,useLayoutEffect:Gf,useMemo:Zf,useReducer:so,useRef:Hf,useState:function(){return so(ua)},useDebugValue:mo,useDeferredValue:function(t,e){var a=Kt();return At===null?vo(a,t,e):Kf(a,At.memoizedState,t,e)},useTransition:function(){var t=so(ua)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Yl(t),e]},useSyncExternalStore:Ef,useId:Wf,useHostTransitionStatus:go,useFormState:Bf,useActionState:Bf,useOptimistic:function(t,e){var a=Kt();return At!==null?_f(a,At,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:oo,useCacheRefresh:Pf},Qn=null,Ql=0;function uu(t){var e=Ql;return Ql+=1,Qn===null&&(Qn=[]),mf(Qn,t,e)}function Vl(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function cu(t,e){throw e.$$typeof===A?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function lp(t){var e=t._init;return e(t._payload)}function ip(t){function e(E,S){if(t){var T=E.deletions;T===null?(E.deletions=[S],E.flags|=16):T.push(S)}}function a(E,S){if(!t)return null;for(;S!==null;)e(E,S),S=S.sibling;return null}function n(E){for(var S=new Map;E!==null;)E.key!==null?S.set(E.key,E):S.set(E.index,E),E=E.sibling;return S}function i(E,S){return E=ea(E,S),E.index=0,E.sibling=null,E}function c(E,S,T){return E.index=T,t?(T=E.alternate,T!==null?(T=T.index,T<S?(E.flags|=67108866,S):T):(E.flags|=67108866,S)):(E.flags|=1048576,S)}function f(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function d(E,S,T,C){return S===null||S.tag!==6?(S=Hc(T,E.mode,C),S.return=E,S):(S=i(S,T),S.return=E,S)}function h(E,S,T,C){var K=T.type;return K===M?j(E,S,T.props.children,C,T.key):S!==null&&(S.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Tt&&lp(K)===S.type)?(S=i(S,T.props),Vl(S,T),S.return=E,S):(S=Xi(T.type,T.key,T.props,null,E.mode,C),Vl(S,T),S.return=E,S)}function O(E,S,T,C){return S===null||S.tag!==4||S.stateNode.containerInfo!==T.containerInfo||S.stateNode.implementation!==T.implementation?(S=Lc(T,E.mode,C),S.return=E,S):(S=i(S,T.children||[]),S.return=E,S)}function j(E,S,T,C,K){return S===null||S.tag!==7?(S=Pa(T,E.mode,C,K),S.return=E,S):(S=i(S,T),S.return=E,S)}function q(E,S,T){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return S=Hc(""+S,E.mode,T),S.return=E,S;if(typeof S=="object"&&S!==null){switch(S.$$typeof){case N:return T=Xi(S.type,S.key,S.props,null,E.mode,T),Vl(T,S),T.return=E,T;case k:return S=Lc(S,E.mode,T),S.return=E,S;case Tt:var C=S._init;return S=C(S._payload),q(E,S,T)}if(bt(S)||Bt(S))return S=Pa(S,E.mode,T,null),S.return=E,S;if(typeof S.then=="function")return q(E,uu(S),T);if(S.$$typeof===Z)return q(E,Ki(E,S),T);cu(E,S)}return null}function R(E,S,T,C){var K=S!==null?S.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return K!==null?null:d(E,S,""+T,C);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case N:return T.key===K?h(E,S,T,C):null;case k:return T.key===K?O(E,S,T,C):null;case Tt:return K=T._init,T=K(T._payload),R(E,S,T,C)}if(bt(T)||Bt(T))return K!==null?null:j(E,S,T,C,null);if(typeof T.then=="function")return R(E,S,uu(T),C);if(T.$$typeof===Z)return R(E,S,Ki(E,T),C);cu(E,T)}return null}function z(E,S,T,C,K){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return E=E.get(T)||null,d(S,E,""+C,K);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case N:return E=E.get(C.key===null?T:C.key)||null,h(S,E,C,K);case k:return E=E.get(C.key===null?T:C.key)||null,O(S,E,C,K);case Tt:var st=C._init;return C=st(C._payload),z(E,S,T,C,K)}if(bt(C)||Bt(C))return E=E.get(T)||null,j(S,E,C,K,null);if(typeof C.then=="function")return z(E,S,T,uu(C),K);if(C.$$typeof===Z)return z(E,S,T,Ki(S,C),K);cu(S,C)}return null}function lt(E,S,T,C){for(var K=null,st=null,$=S,nt=S=0,te=null;$!==null&&nt<T.length;nt++){$.index>nt?(te=$,$=null):te=$.sibling;var gt=R(E,$,T[nt],C);if(gt===null){$===null&&($=te);break}t&&$&&gt.alternate===null&&e(E,$),S=c(gt,S,nt),st===null?K=gt:st.sibling=gt,st=gt,$=te}if(nt===T.length)return a(E,$),yt&&tn(E,nt),K;if($===null){for(;nt<T.length;nt++)$=q(E,T[nt],C),$!==null&&(S=c($,S,nt),st===null?K=$:st.sibling=$,st=$);return yt&&tn(E,nt),K}for($=n($);nt<T.length;nt++)te=z($,E,nt,T[nt],C),te!==null&&(t&&te.alternate!==null&&$.delete(te.key===null?nt:te.key),S=c(te,S,nt),st===null?K=te:st.sibling=te,st=te);return t&&$.forEach(function(Ya){return e(E,Ya)}),yt&&tn(E,nt),K}function et(E,S,T,C){if(T==null)throw Error(o(151));for(var K=null,st=null,$=S,nt=S=0,te=null,gt=T.next();$!==null&&!gt.done;nt++,gt=T.next()){$.index>nt?(te=$,$=null):te=$.sibling;var Ya=R(E,$,gt.value,C);if(Ya===null){$===null&&($=te);break}t&&$&&Ya.alternate===null&&e(E,$),S=c(Ya,S,nt),st===null?K=Ya:st.sibling=Ya,st=Ya,$=te}if(gt.done)return a(E,$),yt&&tn(E,nt),K;if($===null){for(;!gt.done;nt++,gt=T.next())gt=q(E,gt.value,C),gt!==null&&(S=c(gt,S,nt),st===null?K=gt:st.sibling=gt,st=gt);return yt&&tn(E,nt),K}for($=n($);!gt.done;nt++,gt=T.next())gt=z($,E,nt,gt.value,C),gt!==null&&(t&&gt.alternate!==null&&$.delete(gt.key===null?nt:gt.key),S=c(gt,S,nt),st===null?K=gt:st.sibling=gt,st=gt);return t&&$.forEach(function(Ig){return e(E,Ig)}),yt&&tn(E,nt),K}function Rt(E,S,T,C){if(typeof T=="object"&&T!==null&&T.type===M&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case N:t:{for(var K=T.key;S!==null;){if(S.key===K){if(K=T.type,K===M){if(S.tag===7){a(E,S.sibling),C=i(S,T.props.children),C.return=E,E=C;break t}}else if(S.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Tt&&lp(K)===S.type){a(E,S.sibling),C=i(S,T.props),Vl(C,T),C.return=E,E=C;break t}a(E,S);break}else e(E,S);S=S.sibling}T.type===M?(C=Pa(T.props.children,E.mode,C,T.key),C.return=E,E=C):(C=Xi(T.type,T.key,T.props,null,E.mode,C),Vl(C,T),C.return=E,E=C)}return f(E);case k:t:{for(K=T.key;S!==null;){if(S.key===K)if(S.tag===4&&S.stateNode.containerInfo===T.containerInfo&&S.stateNode.implementation===T.implementation){a(E,S.sibling),C=i(S,T.children||[]),C.return=E,E=C;break t}else{a(E,S);break}else e(E,S);S=S.sibling}C=Lc(T,E.mode,C),C.return=E,E=C}return f(E);case Tt:return K=T._init,T=K(T._payload),Rt(E,S,T,C)}if(bt(T))return lt(E,S,T,C);if(Bt(T)){if(K=Bt(T),typeof K!="function")throw Error(o(150));return T=K.call(T),et(E,S,T,C)}if(typeof T.then=="function")return Rt(E,S,uu(T),C);if(T.$$typeof===Z)return Rt(E,S,Ki(E,T),C);cu(E,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,S!==null&&S.tag===6?(a(E,S.sibling),C=i(S,T),C.return=E,E=C):(a(E,S),C=Hc(T,E.mode,C),C.return=E,E=C),f(E)):a(E,S)}return function(E,S,T,C){try{Ql=0;var K=Rt(E,S,T,C);return Qn=null,K}catch($){if($===Nl||$===Fi)throw $;var st=xe(29,$,null,E.mode);return st.lanes=C,st.return=E,st}finally{}}}var Vn=ip(!0),up=ip(!1),Ue=H(null),Qe=null;function Ra(t){var e=t.alternate;G($t,$t.current&1),G(Ue,t),Qe===null&&(e===null||kn.current!==null||e.memoizedState!==null)&&(Qe=t)}function cp(t){if(t.tag===22){if(G($t,$t.current),G(Ue,t),Qe===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Qe=t)}}else za()}function za(){G($t,$t.current),G(Ue,Ue.current)}function ca(t){Q(Ue),Qe===t&&(Qe=null),Q($t)}var $t=H(0);function ou(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||or(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function xo(t,e,a,n){e=t.memoizedState,a=a(n,e),a=a==null?e:x({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var bo={enqueueSetState:function(t,e,a){t=t._reactInternals;var n=Te(),i=Ta(n);i.payload=e,a!=null&&(i.callback=a),e=Aa(t,i,n),e!==null&&(Ae(e,t,n),Bl(e,t,n))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var n=Te(),i=Ta(n);i.tag=1,i.payload=e,a!=null&&(i.callback=a),e=Aa(t,i,n),e!==null&&(Ae(e,t,n),Bl(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=Te(),n=Ta(a);n.tag=2,e!=null&&(n.callback=e),e=Aa(t,n,a),e!==null&&(Ae(e,t,a),Bl(e,t,a))}};function op(t,e,a,n,i,c,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,c,f):e.prototype&&e.prototype.isPureReactComponent?!zl(a,n)||!zl(i,c):!0}function rp(t,e,a,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,n),e.state!==t&&bo.enqueueReplaceState(e,e.state,null)}function on(t,e){var a=e;if("ref"in e){a={};for(var n in e)n!=="ref"&&(a[n]=e[n])}if(t=t.defaultProps){a===e&&(a=x({},a));for(var i in t)a[i]===void 0&&(a[i]=t[i])}return a}var ru=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function sp(t){ru(t)}function fp(t){console.error(t)}function pp(t){ru(t)}function su(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function dp(t,e,a){try{var n=t.onCaughtError;n(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function So(t,e,a){return a=Ta(a),a.tag=3,a.payload={element:null},a.callback=function(){su(t,e)},a}function mp(t){return t=Ta(t),t.tag=3,t}function vp(t,e,a,n){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var c=n.value;t.payload=function(){return i(c)},t.callback=function(){dp(e,a,n)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){dp(e,a,n),typeof i!="function"&&(Ma===null?Ma=new Set([this]):Ma.add(this));var d=n.stack;this.componentDidCatch(n.value,{componentStack:d!==null?d:""})})}function Ih(t,e,a,n,i){if(a.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=a.alternate,e!==null&&Ul(e,a,i,!0),a=Ue.current,a!==null){switch(a.tag){case 13:return Qe===null?Vo():a.alternate===null&&kt===0&&(kt=3),a.flags&=-257,a.flags|=65536,a.lanes=i,n===Fc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([n]):e.add(n),Ko(t,n,i)),!1;case 22:return a.flags|=65536,n===Fc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([n]):a.add(n)),Ko(t,n,i)),!1}throw Error(o(435,a.tag))}return Ko(t,n,i),Vo(),!1}if(yt)return e=Ue.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=i,n!==Gc&&(t=Error(o(422),{cause:n}),jl(_e(t,a)))):(n!==Gc&&(e=Error(o(423),{cause:n}),jl(_e(e,a))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,n=_e(n,a),i=So(t.stateNode,n,i),Pc(t,i),kt!==4&&(kt=2)),!1;var c=Error(o(520),{cause:n});if(c=_e(c,a),Pl===null?Pl=[c]:Pl.push(c),kt!==4&&(kt=2),e===null)return!0;n=_e(n,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=i&-i,a.lanes|=t,t=So(a.stateNode,n,t),Pc(a,t),!1;case 1:if(e=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Ma===null||!Ma.has(c))))return a.flags|=65536,i&=-i,a.lanes|=i,i=mp(i),vp(i,t,a,n),Pc(a,i),!1}a=a.return}while(a!==null);return!1}var hp=Error(o(461)),Pt=!1;function ae(t,e,a,n){e.child=t===null?up(e,null,a,n):Vn(e,t.child,a,n)}function gp(t,e,a,n,i){a=a.render;var c=e.ref;if("ref"in n){var f={};for(var d in n)d!=="ref"&&(f[d]=n[d])}else f=n;return ln(e),n=no(t,e,a,f,c,i),d=lo(),t!==null&&!Pt?(io(t,e,i),oa(t,e,i)):(yt&&d&&kc(e),e.flags|=1,ae(t,e,n,i),e.child)}function yp(t,e,a,n,i){if(t===null){var c=a.type;return typeof c=="function"&&!Bc(c)&&c.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=c,xp(t,e,c,n,i)):(t=Xi(a.type,null,n,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!Do(t,i)){var f=c.memoizedProps;if(a=a.compare,a=a!==null?a:zl,a(f,n)&&t.ref===e.ref)return oa(t,e,i)}return e.flags|=1,t=ea(c,n),t.ref=e.ref,t.return=e,e.child=t}function xp(t,e,a,n,i){if(t!==null){var c=t.memoizedProps;if(zl(c,n)&&t.ref===e.ref)if(Pt=!1,e.pendingProps=n=c,Do(t,i))(t.flags&131072)!==0&&(Pt=!0);else return e.lanes=t.lanes,oa(t,e,i)}return Eo(t,e,a,n,i)}function bp(t,e,a){var n=e.pendingProps,i=n.children,c=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if((e.flags&128)!==0){if(n=c!==null?c.baseLanes|a:a,t!==null){for(i=e.child=t.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;e.childLanes=c&~n}else e.childLanes=0,e.child=null;return Sp(t,e,n,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Ji(e,c!==null?c.cachePool:null),c!==null?xf(e,c):to(),cp(e);else return e.lanes=e.childLanes=536870912,Sp(t,e,c!==null?c.baseLanes|a:a,a)}else c!==null?(Ji(e,c.cachePool),xf(e,c),za(),e.memoizedState=null):(t!==null&&Ji(e,null),to(),za());return ae(t,e,i,a),e.child}function Sp(t,e,a,n){var i=Jc();return i=i===null?null:{parent:Ft._currentValue,pool:i},e.memoizedState={baseLanes:a,cachePool:i},t!==null&&Ji(e,null),to(),cp(e),t!==null&&Ul(t,e,n,!0),null}function fu(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(o(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function Eo(t,e,a,n,i){return ln(e),a=no(t,e,a,n,void 0,i),n=lo(),t!==null&&!Pt?(io(t,e,i),oa(t,e,i)):(yt&&n&&kc(e),e.flags|=1,ae(t,e,a,i),e.child)}function Ep(t,e,a,n,i,c){return ln(e),e.updateQueue=null,a=Sf(e,n,a,i),bf(t),n=lo(),t!==null&&!Pt?(io(t,e,c),oa(t,e,c)):(yt&&n&&kc(e),e.flags|=1,ae(t,e,a,c),e.child)}function Tp(t,e,a,n,i){if(ln(e),e.stateNode===null){var c=Nn,f=a.contextType;typeof f=="object"&&f!==null&&(c=ce(f)),c=new a(n,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=bo,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=n,c.state=e.memoizedState,c.refs={},$c(e),f=a.contextType,c.context=typeof f=="object"&&f!==null?ce(f):Nn,c.state=e.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(xo(e,a,f,n),c.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(f=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),f!==c.state&&bo.enqueueReplaceState(c,c.state,null),Ll(e,n,c,i),Hl(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){c=e.stateNode;var d=e.memoizedProps,h=on(a,d);c.props=h;var O=c.context,j=a.contextType;f=Nn,typeof j=="object"&&j!==null&&(f=ce(j));var q=a.getDerivedStateFromProps;j=typeof q=="function"||typeof c.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,j||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d||O!==f)&&rp(e,c,n,f),Ea=!1;var R=e.memoizedState;c.state=R,Ll(e,n,c,i),Hl(),O=e.memoizedState,d||R!==O||Ea?(typeof q=="function"&&(xo(e,a,q,n),O=e.memoizedState),(h=Ea||op(e,a,h,n,R,O,f))?(j||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=O),c.props=n,c.state=O,c.context=f,n=h):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{c=e.stateNode,Wc(t,e),f=e.memoizedProps,j=on(a,f),c.props=j,q=e.pendingProps,R=c.context,O=a.contextType,h=Nn,typeof O=="object"&&O!==null&&(h=ce(O)),d=a.getDerivedStateFromProps,(O=typeof d=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(f!==q||R!==h)&&rp(e,c,n,h),Ea=!1,R=e.memoizedState,c.state=R,Ll(e,n,c,i),Hl();var z=e.memoizedState;f!==q||R!==z||Ea||t!==null&&t.dependencies!==null&&Zi(t.dependencies)?(typeof d=="function"&&(xo(e,a,d,n),z=e.memoizedState),(j=Ea||op(e,a,j,n,R,z,h)||t!==null&&t.dependencies!==null&&Zi(t.dependencies))?(O||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(n,z,h),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(n,z,h)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=z),c.props=n,c.state=z,c.context=h,n=j):(typeof c.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),n=!1)}return c=n,fu(t,e),n=(e.flags&128)!==0,c||n?(c=e.stateNode,a=n&&typeof a.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&n?(e.child=Vn(e,t.child,null,i),e.child=Vn(e,null,a,i)):ae(t,e,a,i),e.memoizedState=c.state,t=e.child):t=oa(t,e,i),t}function Ap(t,e,a,n){return wl(),e.flags|=256,ae(t,e,a,n),e.child}var To={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ao(t){return{baseLanes:t,cachePool:ff()}}function Oo(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=Me),t}function Op(t,e,a){var n=e.pendingProps,i=!1,c=(e.flags&128)!==0,f;if((f=c)||(f=t!==null&&t.memoizedState===null?!1:($t.current&2)!==0),f&&(i=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(i?Ra(e):za(),yt){var d=Lt,h;if(h=d){t:{for(h=d,d=Xe;h.nodeType!==8;){if(!d){d=null;break t}if(h=Le(h.nextSibling),h===null){d=null;break t}}d=h}d!==null?(e.memoizedState={dehydrated:d,treeContext:Ia!==null?{id:aa,overflow:na}:null,retryLane:536870912,hydrationErrors:null},h=xe(18,null,null,0),h.stateNode=d,h.return=e,e.child=h,se=e,Lt=null,h=!0):h=!1}h||an(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return or(d)?e.lanes=32:e.lanes=536870912,null;ca(e)}return d=n.children,n=n.fallback,i?(za(),i=e.mode,d=pu({mode:"hidden",children:d},i),n=Pa(n,i,a,null),d.return=e,n.return=e,d.sibling=n,e.child=d,i=e.child,i.memoizedState=Ao(a),i.childLanes=Oo(t,f,a),e.memoizedState=To,n):(Ra(e),Ro(e,d))}if(h=t.memoizedState,h!==null&&(d=h.dehydrated,d!==null)){if(c)e.flags&256?(Ra(e),e.flags&=-257,e=zo(t,e,a)):e.memoizedState!==null?(za(),e.child=t.child,e.flags|=128,e=null):(za(),i=n.fallback,d=e.mode,n=pu({mode:"visible",children:n.children},d),i=Pa(i,d,a,null),i.flags|=2,n.return=e,i.return=e,n.sibling=i,e.child=n,Vn(e,t.child,null,a),n=e.child,n.memoizedState=Ao(a),n.childLanes=Oo(t,f,a),e.memoizedState=To,e=i);else if(Ra(e),or(d)){if(f=d.nextSibling&&d.nextSibling.dataset,f)var O=f.dgst;f=O,n=Error(o(419)),n.stack="",n.digest=f,jl({value:n,source:null,stack:null}),e=zo(t,e,a)}else if(Pt||Ul(t,e,a,!1),f=(a&t.childLanes)!==0,Pt||f){if(f=jt,f!==null&&(n=a&-a,n=(n&42)!==0?1:oc(n),n=(n&(f.suspendedLanes|a))!==0?0:n,n!==0&&n!==h.retryLane))throw h.retryLane=n,Cn(t,n),Ae(f,t,n),hp;d.data==="$?"||Vo(),e=zo(t,e,a)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=h.treeContext,Lt=Le(d.nextSibling),se=e,yt=!0,en=null,Xe=!1,t!==null&&(we[je++]=aa,we[je++]=na,we[je++]=Ia,aa=t.id,na=t.overflow,Ia=e),e=Ro(e,n.children),e.flags|=4096);return e}return i?(za(),i=n.fallback,d=e.mode,h=t.child,O=h.sibling,n=ea(h,{mode:"hidden",children:n.children}),n.subtreeFlags=h.subtreeFlags&65011712,O!==null?i=ea(O,i):(i=Pa(i,d,a,null),i.flags|=2),i.return=e,n.return=e,n.sibling=i,e.child=n,n=i,i=e.child,d=t.child.memoizedState,d===null?d=Ao(a):(h=d.cachePool,h!==null?(O=Ft._currentValue,h=h.parent!==O?{parent:O,pool:O}:h):h=ff(),d={baseLanes:d.baseLanes|a,cachePool:h}),i.memoizedState=d,i.childLanes=Oo(t,f,a),e.memoizedState=To,n):(Ra(e),a=t.child,t=a.sibling,a=ea(a,{mode:"visible",children:n.children}),a.return=e,a.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=a,e.memoizedState=null,a)}function Ro(t,e){return e=pu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function pu(t,e){return t=xe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function zo(t,e,a){return Vn(e,t.child,null,a),t=Ro(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Rp(t,e,a){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),Qc(t.return,e,a)}function _o(t,e,a,n,i){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:a,tailMode:i}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=n,c.tail=a,c.tailMode=i)}function zp(t,e,a){var n=e.pendingProps,i=n.revealOrder,c=n.tail;if(ae(t,e,n.children,a),n=$t.current,(n&2)!==0)n=n&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Rp(t,a,e);else if(t.tag===19)Rp(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(G($t,n),i){case"forwards":for(a=e.child,i=null;a!==null;)t=a.alternate,t!==null&&ou(t)===null&&(i=a),a=a.sibling;a=i,a===null?(i=e.child,e.child=null):(i=a.sibling,a.sibling=null),_o(e,!1,i,a,c);break;case"backwards":for(a=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&ou(t)===null){e.child=i;break}t=i.sibling,i.sibling=a,a=i,i=t}_o(e,!0,a,null,c);break;case"together":_o(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function oa(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),Ua|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(Ul(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,a=ea(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=ea(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function Do(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Zi(t)))}function tg(t,e,a){switch(e.tag){case 3:zt(e,e.stateNode.containerInfo),Sa(e,Ft,t.memoizedState.cache),wl();break;case 27:case 5:va(e);break;case 4:zt(e,e.stateNode.containerInfo);break;case 10:Sa(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(Ra(e),e.flags|=128,null):(a&e.child.childLanes)!==0?Op(t,e,a):(Ra(e),t=oa(t,e,a),t!==null?t.sibling:null);Ra(e);break;case 19:var i=(t.flags&128)!==0;if(n=(a&e.childLanes)!==0,n||(Ul(t,e,a,!1),n=(a&e.childLanes)!==0),i){if(n)return zp(t,e,a);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),G($t,$t.current),n)break;return null;case 22:case 23:return e.lanes=0,bp(t,e,a);case 24:Sa(e,Ft,t.memoizedState.cache)}return oa(t,e,a)}function _p(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Pt=!0;else{if(!Do(t,a)&&(e.flags&128)===0)return Pt=!1,tg(t,e,a);Pt=(t.flags&131072)!==0}else Pt=!1,yt&&(e.flags&1048576)!==0&&nf(e,Vi,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,i=n._init;if(n=i(n._payload),e.type=n,typeof n=="function")Bc(n)?(t=on(n,t),e.tag=1,e=Tp(null,e,n,t,a)):(e.tag=0,e=Eo(null,e,n,t,a));else{if(n!=null){if(i=n.$$typeof,i===tt){e.tag=11,e=gp(null,e,n,t,a);break t}else if(i===ot){e.tag=14,e=yp(null,e,n,t,a);break t}}throw e=Mt(n)||n,Error(o(306,e,""))}}return e;case 0:return Eo(t,e,e.type,e.pendingProps,a);case 1:return n=e.type,i=on(n,e.pendingProps),Tp(t,e,n,i,a);case 3:t:{if(zt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));n=e.pendingProps;var c=e.memoizedState;i=c.element,Wc(t,e),Ll(e,n,null,a);var f=e.memoizedState;if(n=f.cache,Sa(e,Ft,n),n!==c.cache&&Vc(e,[Ft],a,!0),Hl(),n=f.element,c.isDehydrated)if(c={element:n,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Ap(t,e,n,a);break t}else if(n!==i){i=_e(Error(o(424)),e),jl(i),e=Ap(t,e,n,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Lt=Le(t.firstChild),se=e,yt=!0,en=null,Xe=!0,a=up(e,null,n,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(wl(),n===i){e=oa(t,e,a);break t}ae(t,e,n,a)}e=e.child}return e;case 26:return fu(t,e),t===null?(a=Ud(e.type,null,e.pendingProps,null))?e.memoizedState=a:yt||(a=e.type,t=e.pendingProps,n=Ru(I.current).createElement(a),n[ue]=e,n[fe]=t,le(n,a,t),Wt(n),e.stateNode=n):e.memoizedState=Ud(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return va(e),t===null&&yt&&(n=e.stateNode=Dd(e.type,e.pendingProps,I.current),se=e,Xe=!0,i=Lt,qa(e.type)?(rr=i,Lt=Le(n.firstChild)):Lt=i),ae(t,e,e.pendingProps.children,a),fu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((i=n=Lt)&&(n=_g(n,e.type,e.pendingProps,Xe),n!==null?(e.stateNode=n,se=e,Lt=Le(n.firstChild),Xe=!1,i=!0):i=!1),i||an(e)),va(e),i=e.type,c=e.pendingProps,f=t!==null?t.memoizedProps:null,n=c.children,ir(i,c)?n=null:f!==null&&ir(i,f)&&(e.flags|=32),e.memoizedState!==null&&(i=no(t,e,Zh,null,null,a),ci._currentValue=i),fu(t,e),ae(t,e,n,a),e.child;case 6:return t===null&&yt&&((t=a=Lt)&&(a=Dg(a,e.pendingProps,Xe),a!==null?(e.stateNode=a,se=e,Lt=null,t=!0):t=!1),t||an(e)),null;case 13:return Op(t,e,a);case 4:return zt(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=Vn(e,null,n,a):ae(t,e,n,a),e.child;case 11:return gp(t,e,e.type,e.pendingProps,a);case 7:return ae(t,e,e.pendingProps,a),e.child;case 8:return ae(t,e,e.pendingProps.children,a),e.child;case 12:return ae(t,e,e.pendingProps.children,a),e.child;case 10:return n=e.pendingProps,Sa(e,e.type,n.value),ae(t,e,n.children,a),e.child;case 9:return i=e.type._context,n=e.pendingProps.children,ln(e),i=ce(i),n=n(i),e.flags|=1,ae(t,e,n,a),e.child;case 14:return yp(t,e,e.type,e.pendingProps,a);case 15:return xp(t,e,e.type,e.pendingProps,a);case 19:return zp(t,e,a);case 31:return n=e.pendingProps,a=e.mode,n={mode:n.mode,children:n.children},t===null?(a=pu(n,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=ea(t.child,n),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return bp(t,e,a);case 24:return ln(e),n=ce(Ft),t===null?(i=Jc(),i===null&&(i=jt,c=Zc(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=a),i=c),e.memoizedState={parent:n,cache:i},$c(e),Sa(e,Ft,i)):((t.lanes&a)!==0&&(Wc(t,e),Ll(e,null,null,a),Hl()),i=t.memoizedState,c=e.memoizedState,i.parent!==n?(i={parent:n,cache:n},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),Sa(e,Ft,n)):(n=c.cache,Sa(e,Ft,n),n!==i.cache&&Vc(e,[Ft],a,!0))),ae(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function ra(t){t.flags|=4}function Dp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Bd(e)){if(e=Ue.current,e!==null&&((vt&4194048)===vt?Qe!==null:(vt&62914560)!==vt&&(vt&536870912)===0||e!==Qe))throw ql=Fc,pf;t.flags|=8192}}function du(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?us():536870912,t.lanes|=e,Fn|=e)}function Zl(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var n=null;a!==null;)a.alternate!==null&&(n=a),a=a.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Ct(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,n=0;if(e)for(var i=t.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags&65011712,n|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags,n|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=n,t.childLanes=a,e}function eg(t,e,a){var n=e.pendingProps;switch(Yc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ct(e),null;case 1:return Ct(e),null;case 3:return a=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),ia(Ft),Ne(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(Dl(e)?ra(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,cf())),Ct(e),null;case 26:return a=e.memoizedState,t===null?(ra(e),a!==null?(Ct(e),Dp(e,a)):(Ct(e),e.flags&=-16777217)):a?a!==t.memoizedState?(ra(e),Ct(e),Dp(e,a)):(Ct(e),e.flags&=-16777217):(t.memoizedProps!==n&&ra(e),Ct(e),e.flags&=-16777217),null;case 27:$e(e),a=I.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(!n){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}t=F.current,Dl(e)?lf(e):(t=Dd(i,n,a),e.stateNode=t,ra(e))}return Ct(e),null;case 5:if($e(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(!n){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}if(t=F.current,Dl(e))lf(e);else{switch(i=Ru(I.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?i.createElement("select",{is:n.is}):i.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?i.createElement(a,{is:n.is}):i.createElement(a)}}t[ue]=e,t[fe]=n;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(le(t,a,n),a){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ra(e)}}return Ct(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(o(166));if(t=I.current,Dl(e)){if(t=e.stateNode,a=e.memoizedProps,n=null,i=se,i!==null)switch(i.tag){case 27:case 5:n=i.memoizedProps}t[ue]=e,t=!!(t.nodeValue===a||n!==null&&n.suppressHydrationWarning===!0||Ed(t.nodeValue,a)),t||an(e)}else t=Ru(t).createTextNode(n),t[ue]=e,e.stateNode=t}return Ct(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=Dl(e),n!==null&&n.dehydrated!==null){if(t===null){if(!i)throw Error(o(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[ue]=e}else wl(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ct(e),i=!1}else i=cf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(ca(e),e):(ca(e),null)}if(ca(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=n!==null,t=t!==null&&t.memoizedState!==null,a){n=e.child,i=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(i=n.alternate.memoizedState.cachePool.pool);var c=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(c=n.memoizedState.cachePool.pool),c!==i&&(n.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),du(e,e.updateQueue),Ct(e),null;case 4:return Ne(),t===null&&tr(e.stateNode.containerInfo),Ct(e),null;case 10:return ia(e.type),Ct(e),null;case 19:if(Q($t),i=e.memoizedState,i===null)return Ct(e),null;if(n=(e.flags&128)!==0,c=i.rendering,c===null)if(n)Zl(i,!1);else{if(kt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=ou(t),c!==null){for(e.flags|=128,Zl(i,!1),t=c.updateQueue,e.updateQueue=t,du(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)af(a,t),a=a.sibling;return G($t,$t.current&1|2),e.child}t=t.sibling}i.tail!==null&&ee()>hu&&(e.flags|=128,n=!0,Zl(i,!1),e.lanes=4194304)}else{if(!n)if(t=ou(c),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,du(e,t),Zl(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!yt)return Ct(e),null}else 2*ee()-i.renderingStartTime>hu&&a!==536870912&&(e.flags|=128,n=!0,Zl(i,!1),e.lanes=4194304);i.isBackwards?(c.sibling=e.child,e.child=c):(t=i.last,t!==null?t.sibling=c:e.child=c,i.last=c)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=ee(),e.sibling=null,t=$t.current,G($t,n?t&1|2:t&1),e):(Ct(e),null);case 22:case 23:return ca(e),eo(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(a&536870912)!==0&&(e.flags&128)===0&&(Ct(e),e.subtreeFlags&6&&(e.flags|=8192)):Ct(e),a=e.updateQueue,a!==null&&du(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==a&&(e.flags|=2048),t!==null&&Q(un),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ia(Ft),Ct(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function ag(t,e){switch(Yc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ia(Ft),Ne(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return $e(e),null;case 13:if(ca(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));wl()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Q($t),null;case 4:return Ne(),null;case 10:return ia(e.type),null;case 22:case 23:return ca(e),eo(),t!==null&&Q(un),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ia(Ft),null;case 25:return null;default:return null}}function wp(t,e){switch(Yc(e),e.tag){case 3:ia(Ft),Ne();break;case 26:case 27:case 5:$e(e);break;case 4:Ne();break;case 13:ca(e);break;case 19:Q($t);break;case 10:ia(e.type);break;case 22:case 23:ca(e),eo(),t!==null&&Q(un);break;case 24:ia(Ft)}}function Kl(t,e){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){n=void 0;var c=a.create,f=a.inst;n=c(),f.destroy=n}a=a.next}while(a!==i)}}catch(d){_t(e,e.return,d)}}function _a(t,e,a){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var c=i.next;n=c;do{if((n.tag&t)===t){var f=n.inst,d=f.destroy;if(d!==void 0){f.destroy=void 0,i=e;var h=a,O=d;try{O()}catch(j){_t(i,h,j)}}}n=n.next}while(n!==c)}}catch(j){_t(e,e.return,j)}}function jp(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{yf(e,a)}catch(n){_t(t,t.return,n)}}}function Up(t,e,a){a.props=on(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(n){_t(t,e,n)}}function Jl(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof a=="function"?t.refCleanup=a(n):a.current=n}}catch(i){_t(t,e,i)}}function Ve(t,e){var a=t.ref,n=t.refCleanup;if(a!==null)if(typeof n=="function")try{n()}catch(i){_t(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){_t(t,e,i)}else a.current=null}function Mp(t){var e=t.type,a=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break t;case"img":a.src?n.src=a.src:a.srcSet&&(n.srcset=a.srcSet)}}catch(i){_t(t,t.return,i)}}function wo(t,e,a){try{var n=t.stateNode;Tg(n,t.type,a,e),n[fe]=e}catch(i){_t(t,t.return,i)}}function Cp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&qa(t.type)||t.tag===4}function jo(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Cp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&qa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Uo(t,e,a){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Ou));else if(n!==4&&(n===27&&qa(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(Uo(t,e,a),t=t.sibling;t!==null;)Uo(t,e,a),t=t.sibling}function mu(t,e,a){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(n!==4&&(n===27&&qa(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(mu(t,e,a),t=t.sibling;t!==null;)mu(t,e,a),t=t.sibling}function Np(t){var e=t.stateNode,a=t.memoizedProps;try{for(var n=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);le(e,n,a),e[ue]=t,e[fe]=a}catch(c){_t(t,t.return,c)}}var sa=!1,Qt=!1,Mo=!1,qp=typeof WeakSet=="function"?WeakSet:Set,It=null;function ng(t,e){if(t=t.containerInfo,nr=Uu,t=Zs(t),wc(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var n=a.getSelection&&a.getSelection();if(n&&n.rangeCount!==0){a=n.anchorNode;var i=n.anchorOffset,c=n.focusNode;n=n.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break t}var f=0,d=-1,h=-1,O=0,j=0,q=t,R=null;e:for(;;){for(var z;q!==a||i!==0&&q.nodeType!==3||(d=f+i),q!==c||n!==0&&q.nodeType!==3||(h=f+n),q.nodeType===3&&(f+=q.nodeValue.length),(z=q.firstChild)!==null;)R=q,q=z;for(;;){if(q===t)break e;if(R===a&&++O===i&&(d=f),R===c&&++j===n&&(h=f),(z=q.nextSibling)!==null)break;q=R,R=q.parentNode}q=z}a=d===-1||h===-1?null:{start:d,end:h}}else a=null}a=a||{start:0,end:0}}else a=null;for(lr={focusedElem:t,selectionRange:a},Uu=!1,It=e;It!==null;)if(e=It,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,It=t;else for(;It!==null;){switch(e=It,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,a=e,i=c.memoizedProps,c=c.memoizedState,n=a.stateNode;try{var lt=on(a.type,i,a.elementType===a.type);t=n.getSnapshotBeforeUpdate(lt,c),n.__reactInternalSnapshotBeforeUpdate=t}catch(et){_t(a,a.return,et)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)cr(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":cr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,It=t;break}It=e.return}}function Bp(t,e,a){var n=a.flags;switch(a.tag){case 0:case 11:case 15:Da(t,a),n&4&&Kl(5,a);break;case 1:if(Da(t,a),n&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(f){_t(a,a.return,f)}else{var i=on(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){_t(a,a.return,f)}}n&64&&jp(a),n&512&&Jl(a,a.return);break;case 3:if(Da(t,a),n&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{yf(t,e)}catch(f){_t(a,a.return,f)}}break;case 27:e===null&&n&4&&Np(a);case 26:case 5:Da(t,a),e===null&&n&4&&Mp(a),n&512&&Jl(a,a.return);break;case 12:Da(t,a);break;case 13:Da(t,a),n&4&&kp(t,a),n&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=pg.bind(null,a),wg(t,a))));break;case 22:if(n=a.memoizedState!==null||sa,!n){e=e!==null&&e.memoizedState!==null||Qt,i=sa;var c=Qt;sa=n,(Qt=e)&&!c?wa(t,a,(a.subtreeFlags&8772)!==0):Da(t,a),sa=i,Qt=c}break;case 30:break;default:Da(t,a)}}function Hp(t){var e=t.alternate;e!==null&&(t.alternate=null,Hp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&fc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ut=null,me=!1;function fa(t,e,a){for(a=a.child;a!==null;)Lp(t,e,a),a=a.sibling}function Lp(t,e,a){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(wt,a)}catch{}switch(a.tag){case 26:Qt||Ve(a,e),fa(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Qt||Ve(a,e);var n=Ut,i=me;qa(a.type)&&(Ut=a.stateNode,me=!1),fa(t,e,a),ni(a.stateNode),Ut=n,me=i;break;case 5:Qt||Ve(a,e);case 6:if(n=Ut,i=me,Ut=null,fa(t,e,a),Ut=n,me=i,Ut!==null)if(me)try{(Ut.nodeType===9?Ut.body:Ut.nodeName==="HTML"?Ut.ownerDocument.body:Ut).removeChild(a.stateNode)}catch(c){_t(a,e,c)}else try{Ut.removeChild(a.stateNode)}catch(c){_t(a,e,c)}break;case 18:Ut!==null&&(me?(t=Ut,zd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),fi(t)):zd(Ut,a.stateNode));break;case 4:n=Ut,i=me,Ut=a.stateNode.containerInfo,me=!0,fa(t,e,a),Ut=n,me=i;break;case 0:case 11:case 14:case 15:Qt||_a(2,a,e),Qt||_a(4,a,e),fa(t,e,a);break;case 1:Qt||(Ve(a,e),n=a.stateNode,typeof n.componentWillUnmount=="function"&&Up(a,e,n)),fa(t,e,a);break;case 21:fa(t,e,a);break;case 22:Qt=(n=Qt)||a.memoizedState!==null,fa(t,e,a),Qt=n;break;default:fa(t,e,a)}}function kp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{fi(t)}catch(a){_t(e,e.return,a)}}function lg(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new qp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new qp),e;default:throw Error(o(435,t.tag))}}function Co(t,e){var a=lg(t);e.forEach(function(n){var i=dg.bind(null,t,n);a.has(n)||(a.add(n),n.then(i,i))})}function be(t,e){var a=e.deletions;if(a!==null)for(var n=0;n<a.length;n++){var i=a[n],c=t,f=e,d=f;t:for(;d!==null;){switch(d.tag){case 27:if(qa(d.type)){Ut=d.stateNode,me=!1;break t}break;case 5:Ut=d.stateNode,me=!1;break t;case 3:case 4:Ut=d.stateNode.containerInfo,me=!0;break t}d=d.return}if(Ut===null)throw Error(o(160));Lp(c,f,i),Ut=null,me=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Yp(e,t),e=e.sibling}var He=null;function Yp(t,e){var a=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:be(e,t),Se(t),n&4&&(_a(3,t,t.return),Kl(3,t),_a(5,t,t.return));break;case 1:be(e,t),Se(t),n&512&&(Qt||a===null||Ve(a,a.return)),n&64&&sa&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?n:a.concat(n))));break;case 26:var i=He;if(be(e,t),Se(t),n&512&&(Qt||a===null||Ve(a,a.return)),n&4){var c=a!==null?a.memoizedState:null;if(n=t.memoizedState,a===null)if(n===null)if(t.stateNode===null){t:{n=t.type,a=t.memoizedProps,i=i.ownerDocument||i;e:switch(n){case"title":c=i.getElementsByTagName("title")[0],(!c||c[yl]||c[ue]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(n),i.head.insertBefore(c,i.querySelector("head > title"))),le(c,n,a),c[ue]=t,Wt(c),n=c;break t;case"link":var f=Nd("link","href",i).get(n+(a.href||""));if(f){for(var d=0;d<f.length;d++)if(c=f[d],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(d,1);break e}}c=i.createElement(n),le(c,n,a),i.head.appendChild(c);break;case"meta":if(f=Nd("meta","content",i).get(n+(a.content||""))){for(d=0;d<f.length;d++)if(c=f[d],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(d,1);break e}}c=i.createElement(n),le(c,n,a),i.head.appendChild(c);break;default:throw Error(o(468,n))}c[ue]=t,Wt(c),n=c}t.stateNode=n}else qd(i,t.type,t.stateNode);else t.stateNode=Cd(i,n,t.memoizedProps);else c!==n?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,n===null?qd(i,t.type,t.stateNode):Cd(i,n,t.memoizedProps)):n===null&&t.stateNode!==null&&wo(t,t.memoizedProps,a.memoizedProps)}break;case 27:be(e,t),Se(t),n&512&&(Qt||a===null||Ve(a,a.return)),a!==null&&n&4&&wo(t,t.memoizedProps,a.memoizedProps);break;case 5:if(be(e,t),Se(t),n&512&&(Qt||a===null||Ve(a,a.return)),t.flags&32){i=t.stateNode;try{zn(i,"")}catch(z){_t(t,t.return,z)}}n&4&&t.stateNode!=null&&(i=t.memoizedProps,wo(t,i,a!==null?a.memoizedProps:i)),n&1024&&(Mo=!0);break;case 6:if(be(e,t),Se(t),n&4){if(t.stateNode===null)throw Error(o(162));n=t.memoizedProps,a=t.stateNode;try{a.nodeValue=n}catch(z){_t(t,t.return,z)}}break;case 3:if(Du=null,i=He,He=zu(e.containerInfo),be(e,t),He=i,Se(t),n&4&&a!==null&&a.memoizedState.isDehydrated)try{fi(e.containerInfo)}catch(z){_t(t,t.return,z)}Mo&&(Mo=!1,Gp(t));break;case 4:n=He,He=zu(t.stateNode.containerInfo),be(e,t),Se(t),He=n;break;case 12:be(e,t),Se(t);break;case 13:be(e,t),Se(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(ko=ee()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,Co(t,n)));break;case 22:i=t.memoizedState!==null;var h=a!==null&&a.memoizedState!==null,O=sa,j=Qt;if(sa=O||i,Qt=j||h,be(e,t),Qt=j,sa=O,Se(t),n&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(a===null||h||sa||Qt||rn(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){h=a=e;try{if(c=h.stateNode,i)f=c.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{d=h.stateNode;var q=h.memoizedProps.style,R=q!=null&&q.hasOwnProperty("display")?q.display:null;d.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(z){_t(h,h.return,z)}}}else if(e.tag===6){if(a===null){h=e;try{h.stateNode.nodeValue=i?"":h.memoizedProps}catch(z){_t(h,h.return,z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(a=n.retryQueue,a!==null&&(n.retryQueue=null,Co(t,a))));break;case 19:be(e,t),Se(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,Co(t,n)));break;case 30:break;case 21:break;default:be(e,t),Se(t)}}function Se(t){var e=t.flags;if(e&2){try{for(var a,n=t.return;n!==null;){if(Cp(n)){a=n;break}n=n.return}if(a==null)throw Error(o(160));switch(a.tag){case 27:var i=a.stateNode,c=jo(t);mu(t,c,i);break;case 5:var f=a.stateNode;a.flags&32&&(zn(f,""),a.flags&=-33);var d=jo(t);mu(t,d,f);break;case 3:case 4:var h=a.stateNode.containerInfo,O=jo(t);Uo(t,O,h);break;default:throw Error(o(161))}}catch(j){_t(t,t.return,j)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Gp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Gp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Da(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Bp(t,e.alternate,e),e=e.sibling}function rn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:_a(4,e,e.return),rn(e);break;case 1:Ve(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&Up(e,e.return,a),rn(e);break;case 27:ni(e.stateNode);case 26:case 5:Ve(e,e.return),rn(e);break;case 22:e.memoizedState===null&&rn(e);break;case 30:rn(e);break;default:rn(e)}t=t.sibling}}function wa(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,i=t,c=e,f=c.flags;switch(c.tag){case 0:case 11:case 15:wa(i,c,a),Kl(4,c);break;case 1:if(wa(i,c,a),n=c,i=n.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(O){_t(n,n.return,O)}if(n=c,i=n.updateQueue,i!==null){var d=n.stateNode;try{var h=i.shared.hiddenCallbacks;if(h!==null)for(i.shared.hiddenCallbacks=null,i=0;i<h.length;i++)gf(h[i],d)}catch(O){_t(n,n.return,O)}}a&&f&64&&jp(c),Jl(c,c.return);break;case 27:Np(c);case 26:case 5:wa(i,c,a),a&&n===null&&f&4&&Mp(c),Jl(c,c.return);break;case 12:wa(i,c,a);break;case 13:wa(i,c,a),a&&f&4&&kp(i,c);break;case 22:c.memoizedState===null&&wa(i,c,a),Jl(c,c.return);break;case 30:break;default:wa(i,c,a)}e=e.sibling}}function No(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&Ml(a))}function qo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ml(t))}function Ze(t,e,a,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xp(t,e,a,n),e=e.sibling}function Xp(t,e,a,n){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Ze(t,e,a,n),i&2048&&Kl(9,e);break;case 1:Ze(t,e,a,n);break;case 3:Ze(t,e,a,n),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ml(t)));break;case 12:if(i&2048){Ze(t,e,a,n),t=e.stateNode;try{var c=e.memoizedProps,f=c.id,d=c.onPostCommit;typeof d=="function"&&d(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(h){_t(e,e.return,h)}}else Ze(t,e,a,n);break;case 13:Ze(t,e,a,n);break;case 23:break;case 22:c=e.stateNode,f=e.alternate,e.memoizedState!==null?c._visibility&2?Ze(t,e,a,n):Fl(t,e):c._visibility&2?Ze(t,e,a,n):(c._visibility|=2,Zn(t,e,a,n,(e.subtreeFlags&10256)!==0)),i&2048&&No(f,e);break;case 24:Ze(t,e,a,n),i&2048&&qo(e.alternate,e);break;default:Ze(t,e,a,n)}}function Zn(t,e,a,n,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,f=e,d=a,h=n,O=f.flags;switch(f.tag){case 0:case 11:case 15:Zn(c,f,d,h,i),Kl(8,f);break;case 23:break;case 22:var j=f.stateNode;f.memoizedState!==null?j._visibility&2?Zn(c,f,d,h,i):Fl(c,f):(j._visibility|=2,Zn(c,f,d,h,i)),i&&O&2048&&No(f.alternate,f);break;case 24:Zn(c,f,d,h,i),i&&O&2048&&qo(f.alternate,f);break;default:Zn(c,f,d,h,i)}e=e.sibling}}function Fl(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,n=e,i=n.flags;switch(n.tag){case 22:Fl(a,n),i&2048&&No(n.alternate,n);break;case 24:Fl(a,n),i&2048&&qo(n.alternate,n);break;default:Fl(a,n)}e=e.sibling}}var $l=8192;function Kn(t){if(t.subtreeFlags&$l)for(t=t.child;t!==null;)Qp(t),t=t.sibling}function Qp(t){switch(t.tag){case 26:Kn(t),t.flags&$l&&t.memoizedState!==null&&Xg(He,t.memoizedState,t.memoizedProps);break;case 5:Kn(t);break;case 3:case 4:var e=He;He=zu(t.stateNode.containerInfo),Kn(t),He=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=$l,$l=16777216,Kn(t),$l=e):Kn(t));break;default:Kn(t)}}function Vp(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Wl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var n=e[a];It=n,Kp(n,t)}Vp(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Zp(t),t=t.sibling}function Zp(t){switch(t.tag){case 0:case 11:case 15:Wl(t),t.flags&2048&&_a(9,t,t.return);break;case 3:Wl(t);break;case 12:Wl(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,vu(t)):Wl(t);break;default:Wl(t)}}function vu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var n=e[a];It=n,Kp(n,t)}Vp(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:_a(8,e,e.return),vu(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,vu(e));break;default:vu(e)}t=t.sibling}}function Kp(t,e){for(;It!==null;){var a=It;switch(a.tag){case 0:case 11:case 15:_a(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var n=a.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Ml(a.memoizedState.cache)}if(n=a.child,n!==null)n.return=a,It=n;else t:for(a=t;It!==null;){n=It;var i=n.sibling,c=n.return;if(Hp(n),n===a){It=null;break t}if(i!==null){i.return=c,It=i;break t}It=c}}}var ig={getCacheForType:function(t){var e=ce(Ft),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},ug=typeof WeakMap=="function"?WeakMap:Map,St=0,jt=null,pt=null,vt=0,Et=0,Ee=null,ja=!1,Jn=!1,Bo=!1,pa=0,kt=0,Ua=0,sn=0,Ho=0,Me=0,Fn=0,Pl=null,ve=null,Lo=!1,ko=0,hu=1/0,gu=null,Ma=null,ne=0,Ca=null,$n=null,Wn=0,Yo=0,Go=null,Jp=null,Il=0,Xo=null;function Te(){if((St&2)!==0&&vt!==0)return vt&-vt;if(w.T!==null){var t=Hn;return t!==0?t:$o()}return rs()}function Fp(){Me===0&&(Me=(vt&536870912)===0||yt?_i():536870912);var t=Ue.current;return t!==null&&(t.flags|=32),Me}function Ae(t,e,a){(t===jt&&(Et===2||Et===9)||t.cancelPendingCommit!==null)&&(Pn(t,0),Na(t,vt,Me,!1)),gl(t,a),((St&2)===0||t!==jt)&&(t===jt&&((St&2)===0&&(sn|=a),kt===4&&Na(t,vt,Me,!1)),Ke(t))}function $p(t,e,a){if((St&6)!==0)throw Error(o(327));var n=!a&&(e&124)===0&&(e&t.expiredLanes)===0||Pe(t,e),i=n?rg(t,e):Zo(t,e,!0),c=n;do{if(i===0){Jn&&!n&&Na(t,e,0,!1);break}else{if(a=t.current.alternate,c&&!cg(a)){i=Zo(t,e,!1),c=!1;continue}if(i===2){if(c=e,t.errorRecoveryDisabledLanes&c)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var d=t;i=Pl;var h=d.current.memoizedState.isDehydrated;if(h&&(Pn(d,f).flags|=256),f=Zo(d,f,!1),f!==2){if(Bo&&!h){d.errorRecoveryDisabledLanes|=c,sn|=c,i=4;break t}c=ve,ve=i,c!==null&&(ve===null?ve=c:ve.push.apply(ve,c))}i=f}if(c=!1,i!==2)continue}}if(i===1){Pn(t,0),Na(t,e,0,!0);break}t:{switch(n=t,c=i,c){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Na(n,e,Me,!ja);break t;case 2:ve=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(i=ko+300-ee(),10<i)){if(Na(n,e,Me,!ja),ya(n,0,!0)!==0)break t;n.timeoutHandle=Od(Wp.bind(null,n,a,ve,gu,Lo,e,Me,sn,Fn,ja,c,2,-0,0),i);break t}Wp(n,a,ve,gu,Lo,e,Me,sn,Fn,ja,c,0,-0,0)}}break}while(!0);Ke(t)}function Wp(t,e,a,n,i,c,f,d,h,O,j,q,R,z){if(t.timeoutHandle=-1,q=e.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(ui={stylesheets:null,count:0,unsuspend:Gg},Qp(e),q=Qg(),q!==null)){t.cancelPendingCommit=q(ld.bind(null,t,e,c,a,n,i,f,d,h,j,1,R,z)),Na(t,c,f,!O);return}ld(t,e,c,a,n,i,f,d,h)}function cg(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var n=0;n<a.length;n++){var i=a[n],c=i.getSnapshot;i=i.value;try{if(!ye(c(),i))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Na(t,e,a,n){e&=~Ho,e&=~sn,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var i=e;0<i;){var c=31-Jt(i),f=1<<c;n[c]=-1,i&=~f}a!==0&&cs(t,a,e)}function yu(){return(St&6)===0?(ti(0),!1):!0}function Qo(){if(pt!==null){if(Et===0)var t=pt.return;else t=pt,la=nn=null,uo(t),Qn=null,Ql=0,t=pt;for(;t!==null;)wp(t.alternate,t),t=t.return;pt=null}}function Pn(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,Og(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),Qo(),jt=t,pt=a=ea(t.current,null),vt=e,Et=0,Ee=null,ja=!1,Jn=Pe(t,e),Bo=!1,Fn=Me=Ho=sn=Ua=kt=0,ve=Pl=null,Lo=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var i=31-Jt(n),c=1<<i;e|=t[i],n&=~c}return pa=e,ki(),a}function Pp(t,e){rt=null,w.H=iu,e===Nl||e===Fi?(e=vf(),Et=3):e===pf?(e=vf(),Et=4):Et=e===hp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ee=e,pt===null&&(kt=1,su(t,_e(e,t.current)))}function Ip(){var t=w.H;return w.H=iu,t===null?iu:t}function td(){var t=w.A;return w.A=ig,t}function Vo(){kt=4,ja||(vt&4194048)!==vt&&Ue.current!==null||(Jn=!0),(Ua&134217727)===0&&(sn&134217727)===0||jt===null||Na(jt,vt,Me,!1)}function Zo(t,e,a){var n=St;St|=2;var i=Ip(),c=td();(jt!==t||vt!==e)&&(gu=null,Pn(t,e)),e=!1;var f=kt;t:do try{if(Et!==0&&pt!==null){var d=pt,h=Ee;switch(Et){case 8:Qo(),f=6;break t;case 3:case 2:case 9:case 6:Ue.current===null&&(e=!0);var O=Et;if(Et=0,Ee=null,In(t,d,h,O),a&&Jn){f=0;break t}break;default:O=Et,Et=0,Ee=null,In(t,d,h,O)}}og(),f=kt;break}catch(j){Pp(t,j)}while(!0);return e&&t.shellSuspendCounter++,la=nn=null,St=n,w.H=i,w.A=c,pt===null&&(jt=null,vt=0,ki()),f}function og(){for(;pt!==null;)ed(pt)}function rg(t,e){var a=St;St|=2;var n=Ip(),i=td();jt!==t||vt!==e?(gu=null,hu=ee()+500,Pn(t,e)):Jn=Pe(t,e);t:do try{if(Et!==0&&pt!==null){e=pt;var c=Ee;e:switch(Et){case 1:Et=0,Ee=null,In(t,e,c,1);break;case 2:case 9:if(df(c)){Et=0,Ee=null,ad(e);break}e=function(){Et!==2&&Et!==9||jt!==t||(Et=7),Ke(t)},c.then(e,e);break t;case 3:Et=7;break t;case 4:Et=5;break t;case 7:df(c)?(Et=0,Ee=null,ad(e)):(Et=0,Ee=null,In(t,e,c,7));break;case 5:var f=null;switch(pt.tag){case 26:f=pt.memoizedState;case 5:case 27:var d=pt;if(!f||Bd(f)){Et=0,Ee=null;var h=d.sibling;if(h!==null)pt=h;else{var O=d.return;O!==null?(pt=O,xu(O)):pt=null}break e}}Et=0,Ee=null,In(t,e,c,5);break;case 6:Et=0,Ee=null,In(t,e,c,6);break;case 8:Qo(),kt=6;break t;default:throw Error(o(462))}}sg();break}catch(j){Pp(t,j)}while(!0);return la=nn=null,w.H=n,w.A=i,St=a,pt!==null?0:(jt=null,vt=0,ki(),kt)}function sg(){for(;pt!==null&&!Oi();)ed(pt)}function ed(t){var e=_p(t.alternate,t,pa);t.memoizedProps=t.pendingProps,e===null?xu(t):pt=e}function ad(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=Ep(a,e,e.pendingProps,e.type,void 0,vt);break;case 11:e=Ep(a,e,e.pendingProps,e.type.render,e.ref,vt);break;case 5:uo(e);default:wp(a,e),e=pt=af(e,pa),e=_p(a,e,pa)}t.memoizedProps=t.pendingProps,e===null?xu(t):pt=e}function In(t,e,a,n){la=nn=null,uo(e),Qn=null,Ql=0;var i=e.return;try{if(Ih(t,i,e,a,vt)){kt=1,su(t,_e(a,t.current)),pt=null;return}}catch(c){if(i!==null)throw pt=i,c;kt=1,su(t,_e(a,t.current)),pt=null;return}e.flags&32768?(yt||n===1?t=!0:Jn||(vt&536870912)!==0?t=!1:(ja=t=!0,(n===2||n===9||n===3||n===6)&&(n=Ue.current,n!==null&&n.tag===13&&(n.flags|=16384))),nd(e,t)):xu(e)}function xu(t){var e=t;do{if((e.flags&32768)!==0){nd(e,ja);return}t=e.return;var a=eg(e.alternate,e,pa);if(a!==null){pt=a;return}if(e=e.sibling,e!==null){pt=e;return}pt=e=t}while(e!==null);kt===0&&(kt=5)}function nd(t,e){do{var a=ag(t.alternate,t);if(a!==null){a.flags&=32767,pt=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){pt=t;return}pt=t=a}while(t!==null);kt=6,pt=null}function ld(t,e,a,n,i,c,f,d,h){t.cancelPendingCommit=null;do bu();while(ne!==0);if((St&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(c=e.lanes|e.childLanes,c|=Nc,Gv(t,a,c,f,d,h),t===jt&&(pt=jt=null,vt=0),$n=e,Ca=t,Wn=a,Yo=c,Go=i,Jp=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,mg(xn,function(){return rd(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=w.T,w.T=null,i=X.p,X.p=2,f=St,St|=4;try{ng(t,e,a)}finally{St=f,X.p=i,w.T=n}}ne=1,id(),ud(),cd()}}function id(){if(ne===1){ne=0;var t=Ca,e=$n,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=w.T,w.T=null;var n=X.p;X.p=2;var i=St;St|=4;try{Yp(e,t);var c=lr,f=Zs(t.containerInfo),d=c.focusedElem,h=c.selectionRange;if(f!==d&&d&&d.ownerDocument&&Vs(d.ownerDocument.documentElement,d)){if(h!==null&&wc(d)){var O=h.start,j=h.end;if(j===void 0&&(j=O),"selectionStart"in d)d.selectionStart=O,d.selectionEnd=Math.min(j,d.value.length);else{var q=d.ownerDocument||document,R=q&&q.defaultView||window;if(R.getSelection){var z=R.getSelection(),lt=d.textContent.length,et=Math.min(h.start,lt),Rt=h.end===void 0?et:Math.min(h.end,lt);!z.extend&&et>Rt&&(f=Rt,Rt=et,et=f);var E=Qs(d,et),S=Qs(d,Rt);if(E&&S&&(z.rangeCount!==1||z.anchorNode!==E.node||z.anchorOffset!==E.offset||z.focusNode!==S.node||z.focusOffset!==S.offset)){var T=q.createRange();T.setStart(E.node,E.offset),z.removeAllRanges(),et>Rt?(z.addRange(T),z.extend(S.node,S.offset)):(T.setEnd(S.node,S.offset),z.addRange(T))}}}}for(q=[],z=d;z=z.parentNode;)z.nodeType===1&&q.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<q.length;d++){var C=q[d];C.element.scrollLeft=C.left,C.element.scrollTop=C.top}}Uu=!!nr,lr=nr=null}finally{St=i,X.p=n,w.T=a}}t.current=e,ne=2}}function ud(){if(ne===2){ne=0;var t=Ca,e=$n,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=w.T,w.T=null;var n=X.p;X.p=2;var i=St;St|=4;try{Bp(t,e.alternate,e)}finally{St=i,X.p=n,w.T=a}}ne=3}}function cd(){if(ne===4||ne===3){ne=0,Ri();var t=Ca,e=$n,a=Wn,n=Jp;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ne=5:(ne=0,$n=Ca=null,od(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(Ma=null),rc(a),e=e.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(wt,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=w.T,i=X.p,X.p=2,w.T=null;try{for(var c=t.onRecoverableError,f=0;f<n.length;f++){var d=n[f];c(d.value,{componentStack:d.stack})}}finally{w.T=e,X.p=i}}(Wn&3)!==0&&bu(),Ke(t),i=t.pendingLanes,(a&4194090)!==0&&(i&42)!==0?t===Xo?Il++:(Il=0,Xo=t):Il=0,ti(0)}}function od(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ml(e)))}function bu(t){return id(),ud(),cd(),rd()}function rd(){if(ne!==5)return!1;var t=Ca,e=Yo;Yo=0;var a=rc(Wn),n=w.T,i=X.p;try{X.p=32>a?32:a,w.T=null,a=Go,Go=null;var c=Ca,f=Wn;if(ne=0,$n=Ca=null,Wn=0,(St&6)!==0)throw Error(o(331));var d=St;if(St|=4,Zp(c.current),Xp(c,c.current,f,a),St=d,ti(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(wt,c)}catch{}return!0}finally{X.p=i,w.T=n,od(t,e)}}function sd(t,e,a){e=_e(a,e),e=So(t.stateNode,e,2),t=Aa(t,e,2),t!==null&&(gl(t,2),Ke(t))}function _t(t,e,a){if(t.tag===3)sd(t,t,a);else for(;e!==null;){if(e.tag===3){sd(e,t,a);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Ma===null||!Ma.has(n))){t=_e(a,t),a=mp(2),n=Aa(e,a,2),n!==null&&(vp(a,n,e,t),gl(n,2),Ke(n));break}}e=e.return}}function Ko(t,e,a){var n=t.pingCache;if(n===null){n=t.pingCache=new ug;var i=new Set;n.set(e,i)}else i=n.get(e),i===void 0&&(i=new Set,n.set(e,i));i.has(a)||(Bo=!0,i.add(a),t=fg.bind(null,t,e,a),e.then(t,t))}function fg(t,e,a){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,jt===t&&(vt&a)===a&&(kt===4||kt===3&&(vt&62914560)===vt&&300>ee()-ko?(St&2)===0&&Pn(t,0):Ho|=a,Fn===vt&&(Fn=0)),Ke(t)}function fd(t,e){e===0&&(e=us()),t=Cn(t,e),t!==null&&(gl(t,e),Ke(t))}function pg(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),fd(t,a)}function dg(t,e){var a=0;switch(t.tag){case 13:var n=t.stateNode,i=t.memoizedState;i!==null&&(a=i.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(o(314))}n!==null&&n.delete(e),fd(t,a)}function mg(t,e){return gn(t,e)}var Su=null,tl=null,Jo=!1,Eu=!1,Fo=!1,fn=0;function Ke(t){t!==tl&&t.next===null&&(tl===null?Su=tl=t:tl=tl.next=t),Eu=!0,Jo||(Jo=!0,hg())}function ti(t,e){if(!Fo&&Eu){Fo=!0;do for(var a=!1,n=Su;n!==null;){if(t!==0){var i=n.pendingLanes;if(i===0)var c=0;else{var f=n.suspendedLanes,d=n.pingedLanes;c=(1<<31-Jt(42|t)+1)-1,c&=i&~(f&~d),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,vd(n,c))}else c=vt,c=ya(n,n===jt?c:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(c&3)===0||Pe(n,c)||(a=!0,vd(n,c));n=n.next}while(a);Fo=!1}}function vg(){pd()}function pd(){Eu=Jo=!1;var t=0;fn!==0&&(Ag()&&(t=fn),fn=0);for(var e=ee(),a=null,n=Su;n!==null;){var i=n.next,c=dd(n,e);c===0?(n.next=null,a===null?Su=i:a.next=i,i===null&&(tl=a)):(a=n,(t!==0||(c&3)!==0)&&(Eu=!0)),n=i}ti(t)}function dd(t,e){for(var a=t.suspendedLanes,n=t.pingedLanes,i=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var f=31-Jt(c),d=1<<f,h=i[f];h===-1?((d&a)===0||(d&n)!==0)&&(i[f]=hl(d,e)):h<=e&&(t.expiredLanes|=d),c&=~d}if(e=jt,a=vt,a=ya(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,a===0||t===e&&(Et===2||Et===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&yn(n),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||Pe(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(n!==null&&yn(n),rc(a)){case 2:case 8:a=Ka;break;case 32:a=xn;break;case 268435456:a=zi;break;default:a=xn}return n=md.bind(null,t),a=gn(a,n),t.callbackPriority=e,t.callbackNode=a,e}return n!==null&&n!==null&&yn(n),t.callbackPriority=2,t.callbackNode=null,2}function md(t,e){if(ne!==0&&ne!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(bu()&&t.callbackNode!==a)return null;var n=vt;return n=ya(t,t===jt?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:($p(t,n,e),dd(t,ee()),t.callbackNode!=null&&t.callbackNode===a?md.bind(null,t):null)}function vd(t,e){if(bu())return null;$p(t,e,!0)}function hg(){Rg(function(){(St&6)!==0?gn(Za,vg):pd()})}function $o(){return fn===0&&(fn=_i()),fn}function hd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Mi(""+t)}function gd(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function gg(t,e,a,n,i){if(e==="submit"&&a&&a.stateNode===i){var c=hd((i[fe]||null).action),f=n.submitter;f&&(e=(e=f[fe]||null)?hd(e.formAction):f.getAttribute("formAction"),e!==null&&(c=e,f=null));var d=new Bi("action","action",null,n,i);t.push({event:d,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(fn!==0){var h=f?gd(i,f):new FormData(i);ho(a,{pending:!0,data:h,method:i.method,action:c},null,h)}}else typeof c=="function"&&(d.preventDefault(),h=f?gd(i,f):new FormData(i),ho(a,{pending:!0,data:h,method:i.method,action:c},c,h))},currentTarget:i}]})}}for(var Wo=0;Wo<Cc.length;Wo++){var Po=Cc[Wo],yg=Po.toLowerCase(),xg=Po[0].toUpperCase()+Po.slice(1);Be(yg,"on"+xg)}Be(Fs,"onAnimationEnd"),Be($s,"onAnimationIteration"),Be(Ws,"onAnimationStart"),Be("dblclick","onDoubleClick"),Be("focusin","onFocus"),Be("focusout","onBlur"),Be(qh,"onTransitionRun"),Be(Bh,"onTransitionStart"),Be(Hh,"onTransitionCancel"),Be(Ps,"onTransitionEnd"),An("onMouseEnter",["mouseout","mouseover"]),An("onMouseLeave",["mouseout","mouseover"]),An("onPointerEnter",["pointerout","pointerover"]),An("onPointerLeave",["pointerout","pointerover"]),Ja("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ja("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ja("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ja("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ja("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ja("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ei="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),bg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ei));function yd(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var n=t[a],i=n.event;n=n.listeners;t:{var c=void 0;if(e)for(var f=n.length-1;0<=f;f--){var d=n[f],h=d.instance,O=d.currentTarget;if(d=d.listener,h!==c&&i.isPropagationStopped())break t;c=d,i.currentTarget=O;try{c(i)}catch(j){ru(j)}i.currentTarget=null,c=h}else for(f=0;f<n.length;f++){if(d=n[f],h=d.instance,O=d.currentTarget,d=d.listener,h!==c&&i.isPropagationStopped())break t;c=d,i.currentTarget=O;try{c(i)}catch(j){ru(j)}i.currentTarget=null,c=h}}}}function dt(t,e){var a=e[sc];a===void 0&&(a=e[sc]=new Set);var n=t+"__bubble";a.has(n)||(xd(e,t,2,!1),a.add(n))}function Io(t,e,a){var n=0;e&&(n|=4),xd(a,t,n,e)}var Tu="_reactListening"+Math.random().toString(36).slice(2);function tr(t){if(!t[Tu]){t[Tu]=!0,fs.forEach(function(a){a!=="selectionchange"&&(bg.has(a)||Io(a,!1,t),Io(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Tu]||(e[Tu]=!0,Io("selectionchange",!1,e))}}function xd(t,e,a,n){switch(Xd(e)){case 2:var i=Kg;break;case 8:i=Jg;break;default:i=mr}a=i.bind(null,e,a,t),i=void 0,!Sc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),n?i!==void 0?t.addEventListener(e,a,{capture:!0,passive:i}):t.addEventListener(e,a,!0):i!==void 0?t.addEventListener(e,a,{passive:i}):t.addEventListener(e,a,!1)}function er(t,e,a,n,i){var c=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var d=n.stateNode.containerInfo;if(d===i)break;if(f===4)for(f=n.return;f!==null;){var h=f.tag;if((h===3||h===4)&&f.stateNode.containerInfo===i)return;f=f.return}for(;d!==null;){if(f=Sn(d),f===null)return;if(h=f.tag,h===5||h===6||h===26||h===27){n=c=f;continue t}d=d.parentNode}}n=n.return}Os(function(){var O=c,j=xc(a),q=[];t:{var R=Is.get(t);if(R!==void 0){var z=Bi,lt=t;switch(t){case"keypress":if(Ni(a)===0)break t;case"keydown":case"keyup":z=mh;break;case"focusin":lt="focus",z=Oc;break;case"focusout":lt="blur",z=Oc;break;case"beforeblur":case"afterblur":z=Oc;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=_s;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=ah;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=gh;break;case Fs:case $s:case Ws:z=ih;break;case Ps:z=xh;break;case"scroll":case"scrollend":z=th;break;case"wheel":z=Sh;break;case"copy":case"cut":case"paste":z=ch;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=ws;break;case"toggle":case"beforetoggle":z=Th}var et=(e&4)!==0,Rt=!et&&(t==="scroll"||t==="scrollend"),E=et?R!==null?R+"Capture":null:R;et=[];for(var S=O,T;S!==null;){var C=S;if(T=C.stateNode,C=C.tag,C!==5&&C!==26&&C!==27||T===null||E===null||(C=bl(S,E),C!=null&&et.push(ai(S,C,T))),Rt)break;S=S.return}0<et.length&&(R=new z(R,lt,null,a,j),q.push({event:R,listeners:et}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",z=t==="mouseout"||t==="pointerout",R&&a!==yc&&(lt=a.relatedTarget||a.fromElement)&&(Sn(lt)||lt[bn]))break t;if((z||R)&&(R=j.window===j?j:(R=j.ownerDocument)?R.defaultView||R.parentWindow:window,z?(lt=a.relatedTarget||a.toElement,z=O,lt=lt?Sn(lt):null,lt!==null&&(Rt=p(lt),et=lt.tag,lt!==Rt||et!==5&&et!==27&&et!==6)&&(lt=null)):(z=null,lt=O),z!==lt)){if(et=_s,C="onMouseLeave",E="onMouseEnter",S="mouse",(t==="pointerout"||t==="pointerover")&&(et=ws,C="onPointerLeave",E="onPointerEnter",S="pointer"),Rt=z==null?R:xl(z),T=lt==null?R:xl(lt),R=new et(C,S+"leave",z,a,j),R.target=Rt,R.relatedTarget=T,C=null,Sn(j)===O&&(et=new et(E,S+"enter",lt,a,j),et.target=T,et.relatedTarget=Rt,C=et),Rt=C,z&&lt)e:{for(et=z,E=lt,S=0,T=et;T;T=el(T))S++;for(T=0,C=E;C;C=el(C))T++;for(;0<S-T;)et=el(et),S--;for(;0<T-S;)E=el(E),T--;for(;S--;){if(et===E||E!==null&&et===E.alternate)break e;et=el(et),E=el(E)}et=null}else et=null;z!==null&&bd(q,R,z,et,!1),lt!==null&&Rt!==null&&bd(q,Rt,lt,et,!0)}}t:{if(R=O?xl(O):window,z=R.nodeName&&R.nodeName.toLowerCase(),z==="select"||z==="input"&&R.type==="file")var K=Hs;else if(qs(R))if(Ls)K=Mh;else{K=jh;var st=wh}else z=R.nodeName,!z||z.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?O&&gc(O.elementType)&&(K=Hs):K=Uh;if(K&&(K=K(t,O))){Bs(q,K,a,j);break t}st&&st(t,R,O),t==="focusout"&&O&&R.type==="number"&&O.memoizedProps.value!=null&&hc(R,"number",R.value)}switch(st=O?xl(O):window,t){case"focusin":(qs(st)||st.contentEditable==="true")&&(jn=st,jc=O,_l=null);break;case"focusout":_l=jc=jn=null;break;case"mousedown":Uc=!0;break;case"contextmenu":case"mouseup":case"dragend":Uc=!1,Ks(q,a,j);break;case"selectionchange":if(Nh)break;case"keydown":case"keyup":Ks(q,a,j)}var $;if(zc)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else wn?Cs(t,a)&&(nt="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(nt="onCompositionStart");nt&&(js&&a.locale!=="ko"&&(wn||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&wn&&($=Rs()):(ba=j,Ec="value"in ba?ba.value:ba.textContent,wn=!0)),st=Au(O,nt),0<st.length&&(nt=new Ds(nt,t,null,a,j),q.push({event:nt,listeners:st}),$?nt.data=$:($=Ns(a),$!==null&&(nt.data=$)))),($=Oh?Rh(t,a):zh(t,a))&&(nt=Au(O,"onBeforeInput"),0<nt.length&&(st=new Ds("onBeforeInput","beforeinput",null,a,j),q.push({event:st,listeners:nt}),st.data=$)),gg(q,t,O,a,j)}yd(q,e)})}function ai(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Au(t,e){for(var a=e+"Capture",n=[];t!==null;){var i=t,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=bl(t,a),i!=null&&n.unshift(ai(t,i,c)),i=bl(t,e),i!=null&&n.push(ai(t,i,c))),t.tag===3)return n;t=t.return}return[]}function el(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function bd(t,e,a,n,i){for(var c=e._reactName,f=[];a!==null&&a!==n;){var d=a,h=d.alternate,O=d.stateNode;if(d=d.tag,h!==null&&h===n)break;d!==5&&d!==26&&d!==27||O===null||(h=O,i?(O=bl(a,c),O!=null&&f.unshift(ai(a,O,h))):i||(O=bl(a,c),O!=null&&f.push(ai(a,O,h)))),a=a.return}f.length!==0&&t.push({event:e,listeners:f})}var Sg=/\r\n?/g,Eg=/\u0000|\uFFFD/g;function Sd(t){return(typeof t=="string"?t:""+t).replace(Sg,`
`).replace(Eg,"")}function Ed(t,e){return e=Sd(e),Sd(t)===e}function Ou(){}function Ot(t,e,a,n,i,c){switch(a){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||zn(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&zn(t,""+n);break;case"className":wi(t,"class",n);break;case"tabIndex":wi(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":wi(t,a,n);break;case"style":Ts(t,n,c);break;case"data":if(e!=="object"){wi(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(a);break}n=Mi(""+n),t.setAttribute(a,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(e!=="input"&&Ot(t,e,"name",i.name,i,null),Ot(t,e,"formEncType",i.formEncType,i,null),Ot(t,e,"formMethod",i.formMethod,i,null),Ot(t,e,"formTarget",i.formTarget,i,null)):(Ot(t,e,"encType",i.encType,i,null),Ot(t,e,"method",i.method,i,null),Ot(t,e,"target",i.target,i,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(a);break}n=Mi(""+n),t.setAttribute(a,n);break;case"onClick":n!=null&&(t.onclick=Ou);break;case"onScroll":n!=null&&dt("scroll",t);break;case"onScrollEnd":n!=null&&dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(o(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(o(60));t.innerHTML=a}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}a=Mi(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,""+n):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":n===!0?t.setAttribute(a,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,n):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(a,n):t.removeAttribute(a);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(a):t.setAttribute(a,n);break;case"popover":dt("beforetoggle",t),dt("toggle",t),Di(t,"popover",n);break;case"xlinkActuate":Ie(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":Ie(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":Ie(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":Ie(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":Ie(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":Ie(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":Di(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Pv.get(a)||a,Di(t,a,n))}}function ar(t,e,a,n,i,c){switch(a){case"style":Ts(t,n,c);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(o(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(o(60));t.innerHTML=a}}break;case"children":typeof n=="string"?zn(t,n):(typeof n=="number"||typeof n=="bigint")&&zn(t,""+n);break;case"onScroll":n!=null&&dt("scroll",t);break;case"onScrollEnd":n!=null&&dt("scrollend",t);break;case"onClick":n!=null&&(t.onclick=Ou);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ps.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),e=a.slice(2,i?a.length-7:void 0),c=t[fe]||null,c=c!=null?c[a]:null,typeof c=="function"&&t.removeEventListener(e,c,i),typeof n=="function")){typeof c!="function"&&c!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,n,i);break t}a in t?t[a]=n:n===!0?t.setAttribute(a,""):Di(t,a,n)}}}function le(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":dt("error",t),dt("load",t);var n=!1,i=!1,c;for(c in a)if(a.hasOwnProperty(c)){var f=a[c];if(f!=null)switch(c){case"src":n=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Ot(t,e,c,f,a,null)}}i&&Ot(t,e,"srcSet",a.srcSet,a,null),n&&Ot(t,e,"src",a.src,a,null);return;case"input":dt("invalid",t);var d=c=f=i=null,h=null,O=null;for(n in a)if(a.hasOwnProperty(n)){var j=a[n];if(j!=null)switch(n){case"name":i=j;break;case"type":f=j;break;case"checked":h=j;break;case"defaultChecked":O=j;break;case"value":c=j;break;case"defaultValue":d=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(o(137,e));break;default:Ot(t,e,n,j,a,null)}}xs(t,c,d,h,O,f,i,!1),ji(t);return;case"select":dt("invalid",t),n=f=c=null;for(i in a)if(a.hasOwnProperty(i)&&(d=a[i],d!=null))switch(i){case"value":c=d;break;case"defaultValue":f=d;break;case"multiple":n=d;default:Ot(t,e,i,d,a,null)}e=c,a=f,t.multiple=!!n,e!=null?Rn(t,!!n,e,!1):a!=null&&Rn(t,!!n,a,!0);return;case"textarea":dt("invalid",t),c=i=n=null;for(f in a)if(a.hasOwnProperty(f)&&(d=a[f],d!=null))switch(f){case"value":n=d;break;case"defaultValue":i=d;break;case"children":c=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(o(91));break;default:Ot(t,e,f,d,a,null)}Ss(t,n,i,c),ji(t);return;case"option":for(h in a)if(a.hasOwnProperty(h)&&(n=a[h],n!=null))switch(h){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:Ot(t,e,h,n,a,null)}return;case"dialog":dt("beforetoggle",t),dt("toggle",t),dt("cancel",t),dt("close",t);break;case"iframe":case"object":dt("load",t);break;case"video":case"audio":for(n=0;n<ei.length;n++)dt(ei[n],t);break;case"image":dt("error",t),dt("load",t);break;case"details":dt("toggle",t);break;case"embed":case"source":case"link":dt("error",t),dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in a)if(a.hasOwnProperty(O)&&(n=a[O],n!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Ot(t,e,O,n,a,null)}return;default:if(gc(e)){for(j in a)a.hasOwnProperty(j)&&(n=a[j],n!==void 0&&ar(t,e,j,n,a,void 0));return}}for(d in a)a.hasOwnProperty(d)&&(n=a[d],n!=null&&Ot(t,e,d,n,a,null))}function Tg(t,e,a,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,f=null,d=null,h=null,O=null,j=null;for(z in a){var q=a[z];if(a.hasOwnProperty(z)&&q!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":h=q;default:n.hasOwnProperty(z)||Ot(t,e,z,null,n,q)}}for(var R in n){var z=n[R];if(q=a[R],n.hasOwnProperty(R)&&(z!=null||q!=null))switch(R){case"type":c=z;break;case"name":i=z;break;case"checked":O=z;break;case"defaultChecked":j=z;break;case"value":f=z;break;case"defaultValue":d=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(o(137,e));break;default:z!==q&&Ot(t,e,R,z,n,q)}}vc(t,f,d,h,O,j,c,i);return;case"select":z=f=d=R=null;for(c in a)if(h=a[c],a.hasOwnProperty(c)&&h!=null)switch(c){case"value":break;case"multiple":z=h;default:n.hasOwnProperty(c)||Ot(t,e,c,null,n,h)}for(i in n)if(c=n[i],h=a[i],n.hasOwnProperty(i)&&(c!=null||h!=null))switch(i){case"value":R=c;break;case"defaultValue":d=c;break;case"multiple":f=c;default:c!==h&&Ot(t,e,i,c,n,h)}e=d,a=f,n=z,R!=null?Rn(t,!!a,R,!1):!!n!=!!a&&(e!=null?Rn(t,!!a,e,!0):Rn(t,!!a,a?[]:"",!1));return;case"textarea":z=R=null;for(d in a)if(i=a[d],a.hasOwnProperty(d)&&i!=null&&!n.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Ot(t,e,d,null,n,i)}for(f in n)if(i=n[f],c=a[f],n.hasOwnProperty(f)&&(i!=null||c!=null))switch(f){case"value":R=i;break;case"defaultValue":z=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==c&&Ot(t,e,f,i,n,c)}bs(t,R,z);return;case"option":for(var lt in a)if(R=a[lt],a.hasOwnProperty(lt)&&R!=null&&!n.hasOwnProperty(lt))switch(lt){case"selected":t.selected=!1;break;default:Ot(t,e,lt,null,n,R)}for(h in n)if(R=n[h],z=a[h],n.hasOwnProperty(h)&&R!==z&&(R!=null||z!=null))switch(h){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:Ot(t,e,h,R,n,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var et in a)R=a[et],a.hasOwnProperty(et)&&R!=null&&!n.hasOwnProperty(et)&&Ot(t,e,et,null,n,R);for(O in n)if(R=n[O],z=a[O],n.hasOwnProperty(O)&&R!==z&&(R!=null||z!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:Ot(t,e,O,R,n,z)}return;default:if(gc(e)){for(var Rt in a)R=a[Rt],a.hasOwnProperty(Rt)&&R!==void 0&&!n.hasOwnProperty(Rt)&&ar(t,e,Rt,void 0,n,R);for(j in n)R=n[j],z=a[j],!n.hasOwnProperty(j)||R===z||R===void 0&&z===void 0||ar(t,e,j,R,n,z);return}}for(var E in a)R=a[E],a.hasOwnProperty(E)&&R!=null&&!n.hasOwnProperty(E)&&Ot(t,e,E,null,n,R);for(q in n)R=n[q],z=a[q],!n.hasOwnProperty(q)||R===z||R==null&&z==null||Ot(t,e,q,R,n,z)}var nr=null,lr=null;function Ru(t){return t.nodeType===9?t:t.ownerDocument}function Td(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ad(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ir(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ur=null;function Ag(){var t=window.event;return t&&t.type==="popstate"?t===ur?!1:(ur=t,!0):(ur=null,!1)}var Od=typeof setTimeout=="function"?setTimeout:void 0,Og=typeof clearTimeout=="function"?clearTimeout:void 0,Rd=typeof Promise=="function"?Promise:void 0,Rg=typeof queueMicrotask=="function"?queueMicrotask:typeof Rd<"u"?function(t){return Rd.resolve(null).then(t).catch(zg)}:Od;function zg(t){setTimeout(function(){throw t})}function qa(t){return t==="head"}function zd(t,e){var a=e,n=0,i=0;do{var c=a.nextSibling;if(t.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<n&&8>n){a=n;var f=t.ownerDocument;if(a&1&&ni(f.documentElement),a&2&&ni(f.body),a&4)for(a=f.head,ni(a),f=a.firstChild;f;){var d=f.nextSibling,h=f.nodeName;f[yl]||h==="SCRIPT"||h==="STYLE"||h==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=d}}if(i===0){t.removeChild(c),fi(e);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:n=a.charCodeAt(0)-48;else n=0;a=c}while(a);fi(e)}function cr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":cr(a),fc(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function _g(t,e,a,n){for(;t.nodeType===1;){var i=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[yl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=Le(t.nextSibling),t===null)break}return null}function Dg(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=Le(t.nextSibling),t===null))return null;return t}function or(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function wg(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var n=function(){e(),a.removeEventListener("DOMContentLoaded",n)};a.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function Le(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var rr=null;function _d(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function Dd(t,e,a){switch(e=Ru(a),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function ni(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);fc(t)}var Ce=new Map,wd=new Set;function zu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var da=X.d;X.d={f:jg,r:Ug,D:Mg,C:Cg,L:Ng,m:qg,X:Hg,S:Bg,M:Lg};function jg(){var t=da.f(),e=yu();return t||e}function Ug(t){var e=En(t);e!==null&&e.tag===5&&e.type==="form"?$f(e):da.r(t)}var al=typeof document>"u"?null:document;function jd(t,e,a){var n=al;if(n&&typeof e=="string"&&e){var i=ze(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),wd.has(i)||(wd.add(i),t={rel:t,crossOrigin:a,href:e},n.querySelector(i)===null&&(e=n.createElement("link"),le(e,"link",t),Wt(e),n.head.appendChild(e)))}}function Mg(t){da.D(t),jd("dns-prefetch",t,null)}function Cg(t,e){da.C(t,e),jd("preconnect",t,e)}function Ng(t,e,a){da.L(t,e,a);var n=al;if(n&&t&&e){var i='link[rel="preload"][as="'+ze(e)+'"]';e==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+ze(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+ze(a.imageSizes)+'"]')):i+='[href="'+ze(t)+'"]';var c=i;switch(e){case"style":c=nl(t);break;case"script":c=ll(t)}Ce.has(c)||(t=x({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),Ce.set(c,t),n.querySelector(i)!==null||e==="style"&&n.querySelector(li(c))||e==="script"&&n.querySelector(ii(c))||(e=n.createElement("link"),le(e,"link",t),Wt(e),n.head.appendChild(e)))}}function qg(t,e){da.m(t,e);var a=al;if(a&&t){var n=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+ze(n)+'"][href="'+ze(t)+'"]',c=i;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=ll(t)}if(!Ce.has(c)&&(t=x({rel:"modulepreload",href:t},e),Ce.set(c,t),a.querySelector(i)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(ii(c)))return}n=a.createElement("link"),le(n,"link",t),Wt(n),a.head.appendChild(n)}}}function Bg(t,e,a){da.S(t,e,a);var n=al;if(n&&t){var i=Tn(n).hoistableStyles,c=nl(t);e=e||"default";var f=i.get(c);if(!f){var d={loading:0,preload:null};if(f=n.querySelector(li(c)))d.loading=5;else{t=x({rel:"stylesheet",href:t,"data-precedence":e},a),(a=Ce.get(c))&&sr(t,a);var h=f=n.createElement("link");Wt(h),le(h,"link",t),h._p=new Promise(function(O,j){h.onload=O,h.onerror=j}),h.addEventListener("load",function(){d.loading|=1}),h.addEventListener("error",function(){d.loading|=2}),d.loading|=4,_u(f,e,n)}f={type:"stylesheet",instance:f,count:1,state:d},i.set(c,f)}}}function Hg(t,e){da.X(t,e);var a=al;if(a&&t){var n=Tn(a).hoistableScripts,i=ll(t),c=n.get(i);c||(c=a.querySelector(ii(i)),c||(t=x({src:t,async:!0},e),(e=Ce.get(i))&&fr(t,e),c=a.createElement("script"),Wt(c),le(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},n.set(i,c))}}function Lg(t,e){da.M(t,e);var a=al;if(a&&t){var n=Tn(a).hoistableScripts,i=ll(t),c=n.get(i);c||(c=a.querySelector(ii(i)),c||(t=x({src:t,async:!0,type:"module"},e),(e=Ce.get(i))&&fr(t,e),c=a.createElement("script"),Wt(c),le(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},n.set(i,c))}}function Ud(t,e,a,n){var i=(i=I.current)?zu(i):null;if(!i)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=nl(a.href),a=Tn(i).hoistableStyles,n=a.get(e),n||(n={type:"style",instance:null,count:0,state:null},a.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=nl(a.href);var c=Tn(i).hoistableStyles,f=c.get(t);if(f||(i=i.ownerDocument||i,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,f),(c=i.querySelector(li(t)))&&!c._p&&(f.instance=c,f.state.loading=5),Ce.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Ce.set(t,a),c||kg(i,t,a,f.state))),e&&n===null)throw Error(o(528,""));return f}if(e&&n!==null)throw Error(o(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=ll(a),a=Tn(i).hoistableScripts,n=a.get(e),n||(n={type:"script",instance:null,count:0,state:null},a.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function nl(t){return'href="'+ze(t)+'"'}function li(t){return'link[rel="stylesheet"]['+t+"]"}function Md(t){return x({},t,{"data-precedence":t.precedence,precedence:null})}function kg(t,e,a,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),le(e,"link",a),Wt(e),t.head.appendChild(e))}function ll(t){return'[src="'+ze(t)+'"]'}function ii(t){return"script[async]"+t}function Cd(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+ze(a.href)+'"]');if(n)return e.instance=n,Wt(n),n;var i=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),Wt(n),le(n,"style",i),_u(n,a.precedence,t),e.instance=n;case"stylesheet":i=nl(a.href);var c=t.querySelector(li(i));if(c)return e.state.loading|=4,e.instance=c,Wt(c),c;n=Md(a),(i=Ce.get(i))&&sr(n,i),c=(t.ownerDocument||t).createElement("link"),Wt(c);var f=c;return f._p=new Promise(function(d,h){f.onload=d,f.onerror=h}),le(c,"link",n),e.state.loading|=4,_u(c,a.precedence,t),e.instance=c;case"script":return c=ll(a.src),(i=t.querySelector(ii(c)))?(e.instance=i,Wt(i),i):(n=a,(i=Ce.get(c))&&(n=x({},a),fr(n,i)),t=t.ownerDocument||t,i=t.createElement("script"),Wt(i),le(i,"link",n),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,_u(n,a.precedence,t));return e.instance}function _u(t,e,a){for(var n=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=n.length?n[n.length-1]:null,c=i,f=0;f<n.length;f++){var d=n[f];if(d.dataset.precedence===e)c=d;else if(c!==i)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function sr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function fr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Du=null;function Nd(t,e,a){if(Du===null){var n=new Map,i=Du=new Map;i.set(a,n)}else i=Du,n=i.get(a),n||(n=new Map,i.set(a,n));if(n.has(t))return n;for(n.set(t,null),a=a.getElementsByTagName(t),i=0;i<a.length;i++){var c=a[i];if(!(c[yl]||c[ue]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var f=c.getAttribute(e)||"";f=t+f;var d=n.get(f);d?d.push(c):n.set(f,[c])}}return n}function qd(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function Yg(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Bd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var ui=null;function Gg(){}function Xg(t,e,a){if(ui===null)throw Error(o(475));var n=ui;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var i=nl(a.href),c=t.querySelector(li(i));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=wu.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=c,Wt(c);return}c=t.ownerDocument||t,a=Md(a),(i=Ce.get(i))&&sr(a,i),c=c.createElement("link"),Wt(c);var f=c;f._p=new Promise(function(d,h){f.onload=d,f.onerror=h}),le(c,"link",a),e.instance=c}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(n.count++,e=wu.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function Qg(){if(ui===null)throw Error(o(475));var t=ui;return t.stylesheets&&t.count===0&&pr(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&pr(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function wu(){if(this.count--,this.count===0){if(this.stylesheets)pr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var ju=null;function pr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,ju=new Map,e.forEach(Vg,t),ju=null,wu.call(t))}function Vg(t,e){if(!(e.state.loading&4)){var a=ju.get(t);if(a)var n=a.get(null);else{a=new Map,ju.set(t,a);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var f=i[c];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),n=f)}n&&a.set(null,n)}i=e.instance,f=i.getAttribute("data-precedence"),c=a.get(f)||n,c===n&&a.set(null,i),a.set(f,i),this.count++,n=wu.bind(this),i.addEventListener("load",n),i.addEventListener("error",n),c?c.parentNode.insertBefore(i,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var ci={$$typeof:Z,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function Zg(t,e,a,n,i,c,f,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=cc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=cc(0),this.hiddenUpdates=cc(null),this.identifierPrefix=n,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function Hd(t,e,a,n,i,c,f,d,h,O,j,q){return t=new Zg(t,e,a,f,d,h,O,q),e=1,c===!0&&(e|=24),c=xe(3,null,null,e),t.current=c,c.stateNode=t,e=Zc(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:n,isDehydrated:a,cache:e},$c(c),t}function Ld(t){return t?(t=Nn,t):Nn}function kd(t,e,a,n,i,c){i=Ld(i),n.context===null?n.context=i:n.pendingContext=i,n=Ta(e),n.payload={element:a},c=c===void 0?null:c,c!==null&&(n.callback=c),a=Aa(t,n,e),a!==null&&(Ae(a,t,e),Bl(a,t,e))}function Yd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function dr(t,e){Yd(t,e),(t=t.alternate)&&Yd(t,e)}function Gd(t){if(t.tag===13){var e=Cn(t,67108864);e!==null&&Ae(e,t,67108864),dr(t,67108864)}}var Uu=!0;function Kg(t,e,a,n){var i=w.T;w.T=null;var c=X.p;try{X.p=2,mr(t,e,a,n)}finally{X.p=c,w.T=i}}function Jg(t,e,a,n){var i=w.T;w.T=null;var c=X.p;try{X.p=8,mr(t,e,a,n)}finally{X.p=c,w.T=i}}function mr(t,e,a,n){if(Uu){var i=vr(n);if(i===null)er(t,e,n,Mu,a),Qd(t,n);else if($g(i,t,e,a,n))n.stopPropagation();else if(Qd(t,n),e&4&&-1<Fg.indexOf(t)){for(;i!==null;){var c=En(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var f=qe(c.pendingLanes);if(f!==0){var d=c;for(d.pendingLanes|=2,d.entangledLanes|=2;f;){var h=1<<31-Jt(f);d.entanglements[1]|=h,f&=~h}Ke(c),(St&6)===0&&(hu=ee()+500,ti(0))}}break;case 13:d=Cn(c,2),d!==null&&Ae(d,c,2),yu(),dr(c,2)}if(c=vr(n),c===null&&er(t,e,n,Mu,a),c===i)break;i=c}i!==null&&n.stopPropagation()}else er(t,e,n,null,a)}}function vr(t){return t=xc(t),hr(t)}var Mu=null;function hr(t){if(Mu=null,t=Sn(t),t!==null){var e=p(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=m(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Mu=t,null}function Xd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(dl()){case Za:return 2;case Ka:return 8;case xn:case uc:return 32;case zi:return 268435456;default:return 32}default:return 32}}var gr=!1,Ba=null,Ha=null,La=null,oi=new Map,ri=new Map,ka=[],Fg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Qd(t,e){switch(t){case"focusin":case"focusout":Ba=null;break;case"dragenter":case"dragleave":Ha=null;break;case"mouseover":case"mouseout":La=null;break;case"pointerover":case"pointerout":oi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":ri.delete(e.pointerId)}}function si(t,e,a,n,i,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:a,eventSystemFlags:n,nativeEvent:c,targetContainers:[i]},e!==null&&(e=En(e),e!==null&&Gd(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function $g(t,e,a,n,i){switch(e){case"focusin":return Ba=si(Ba,t,e,a,n,i),!0;case"dragenter":return Ha=si(Ha,t,e,a,n,i),!0;case"mouseover":return La=si(La,t,e,a,n,i),!0;case"pointerover":var c=i.pointerId;return oi.set(c,si(oi.get(c)||null,t,e,a,n,i)),!0;case"gotpointercapture":return c=i.pointerId,ri.set(c,si(ri.get(c)||null,t,e,a,n,i)),!0}return!1}function Vd(t){var e=Sn(t.target);if(e!==null){var a=p(e);if(a!==null){if(e=a.tag,e===13){if(e=m(a),e!==null){t.blockedOn=e,Xv(t.priority,function(){if(a.tag===13){var n=Te();n=oc(n);var i=Cn(a,n);i!==null&&Ae(i,a,n),dr(a,n)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Cu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=vr(t.nativeEvent);if(a===null){a=t.nativeEvent;var n=new a.constructor(a.type,a);yc=n,a.target.dispatchEvent(n),yc=null}else return e=En(a),e!==null&&Gd(e),t.blockedOn=a,!1;e.shift()}return!0}function Zd(t,e,a){Cu(t)&&a.delete(e)}function Wg(){gr=!1,Ba!==null&&Cu(Ba)&&(Ba=null),Ha!==null&&Cu(Ha)&&(Ha=null),La!==null&&Cu(La)&&(La=null),oi.forEach(Zd),ri.forEach(Zd)}function Nu(t,e){t.blockedOn===e&&(t.blockedOn=null,gr||(gr=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,Wg)))}var qu=null;function Kd(t){qu!==t&&(qu=t,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){qu===t&&(qu=null);for(var e=0;e<t.length;e+=3){var a=t[e],n=t[e+1],i=t[e+2];if(typeof n!="function"){if(hr(n||a)===null)continue;break}var c=En(a);c!==null&&(t.splice(e,3),e-=3,ho(c,{pending:!0,data:i,method:a.method,action:n},n,i))}}))}function fi(t){function e(h){return Nu(h,t)}Ba!==null&&Nu(Ba,t),Ha!==null&&Nu(Ha,t),La!==null&&Nu(La,t),oi.forEach(e),ri.forEach(e);for(var a=0;a<ka.length;a++){var n=ka[a];n.blockedOn===t&&(n.blockedOn=null)}for(;0<ka.length&&(a=ka[0],a.blockedOn===null);)Vd(a),a.blockedOn===null&&ka.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(n=0;n<a.length;n+=3){var i=a[n],c=a[n+1],f=i[fe]||null;if(typeof c=="function")f||Kd(a);else if(f){var d=null;if(c&&c.hasAttribute("formAction")){if(i=c,f=c[fe]||null)d=f.formAction;else if(hr(i)!==null)continue}else d=f.action;typeof d=="function"?a[n+1]=d:(a.splice(n,3),n-=3),Kd(a)}}}function yr(t){this._internalRoot=t}Bu.prototype.render=yr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var a=e.current,n=Te();kd(a,n,t,e,null,null)},Bu.prototype.unmount=yr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;kd(t.current,2,null,t,null,null),yu(),e[bn]=null}};function Bu(t){this._internalRoot=t}Bu.prototype.unstable_scheduleHydration=function(t){if(t){var e=rs();t={blockedOn:null,target:t,priority:e};for(var a=0;a<ka.length&&e!==0&&e<ka[a].priority;a++);ka.splice(a,0,t),a===0&&Vd(t)}};var Jd=u.version;if(Jd!=="19.1.1")throw Error(o(527,Jd,"19.1.1"));X.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=y(e),t=t!==null?v(t):null,t=t===null?null:t.stateNode,t};var Pg={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:w,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hu.isDisabled&&Hu.supportsFiber)try{wt=Hu.inject(Pg),ht=Hu}catch{}}return di.createRoot=function(t,e){if(!s(t))throw Error(o(299));var a=!1,n="",i=sp,c=fp,f=pp,d=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=Hd(t,1,!1,null,null,a,n,i,c,f,d,null),t[bn]=e.current,tr(t),new yr(e)},di.hydrateRoot=function(t,e,a){if(!s(t))throw Error(o(299));var n=!1,i="",c=sp,f=fp,d=pp,h=null,O=null;return a!=null&&(a.unstable_strictMode===!0&&(n=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(d=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(h=a.unstable_transitionCallbacks),a.formState!==void 0&&(O=a.formState)),e=Hd(t,1,!0,e,a??null,n,i,c,f,d,h,O),e.context=Ld(null),a=e.current,n=Te(),n=oc(n),i=Ta(n),i.callback=null,Aa(a,i,n),a=n,e.current.lanes=a,gl(e,a),Ke(e),t[bn]=e.current,tr(t),new Bu(e)},di.version="19.1.1",di}var lm;function ry(){if(lm)return Sr.exports;lm=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(u){console.error(u)}}return l(),Sr.exports=oy(),Sr.exports}var sy=ry();Qm();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gi(){return gi=Object.assign?Object.assign.bind():function(l){for(var u=1;u<arguments.length;u++){var r=arguments[u];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(l[o]=r[o])}return l},gi.apply(this,arguments)}var Xa;(function(l){l.Pop="POP",l.Push="PUSH",l.Replace="REPLACE"})(Xa||(Xa={}));const im="popstate";function fy(l){l===void 0&&(l={});function u(o,s){let{pathname:p,search:m,hash:g}=o.location;return Nr("",{pathname:p,search:m,hash:g},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function r(o,s){return typeof s=="string"?s:Qu(s)}return dy(u,r,null,l)}function qt(l,u){if(l===!1||l===null||typeof l>"u")throw new Error(u)}function Vm(l,u){if(!l){typeof console<"u"&&console.warn(u);try{throw new Error(u)}catch{}}}function py(){return Math.random().toString(36).substr(2,8)}function um(l,u){return{usr:l.state,key:l.key,idx:u}}function Nr(l,u,r,o){return r===void 0&&(r=null),gi({pathname:typeof l=="string"?l:l.pathname,search:"",hash:""},typeof u=="string"?ol(u):u,{state:r,key:u&&u.key||o||py()})}function Qu(l){let{pathname:u="/",search:r="",hash:o=""}=l;return r&&r!=="?"&&(u+=r.charAt(0)==="?"?r:"?"+r),o&&o!=="#"&&(u+=o.charAt(0)==="#"?o:"#"+o),u}function ol(l){let u={};if(l){let r=l.indexOf("#");r>=0&&(u.hash=l.substr(r),l=l.substr(0,r));let o=l.indexOf("?");o>=0&&(u.search=l.substr(o),l=l.substr(0,o)),l&&(u.pathname=l)}return u}function dy(l,u,r,o){o===void 0&&(o={});let{window:s=document.defaultView,v5Compat:p=!1}=o,m=s.history,g=Xa.Pop,y=null,v=x();v==null&&(v=0,m.replaceState(gi({},m.state,{idx:v}),""));function x(){return(m.state||{idx:null}).idx}function A(){g=Xa.Pop;let U=x(),W=U==null?null:U-v;v=U,y&&y({action:g,location:L.location,delta:W})}function N(U,W){g=Xa.Push;let J=Nr(L.location,U,W);v=x()+1;let Z=um(J,v),tt=L.createHref(J);try{m.pushState(Z,"",tt)}catch(Y){if(Y instanceof DOMException&&Y.name==="DataCloneError")throw Y;s.location.assign(tt)}p&&y&&y({action:g,location:L.location,delta:1})}function k(U,W){g=Xa.Replace;let J=Nr(L.location,U,W);v=x();let Z=um(J,v),tt=L.createHref(J);m.replaceState(Z,"",tt),p&&y&&y({action:g,location:L.location,delta:0})}function M(U){let W=s.location.origin!=="null"?s.location.origin:s.location.href,J=typeof U=="string"?U:Qu(U);return J=J.replace(/ $/,"%20"),qt(W,"No window.location.(origin|href) available to create URL for href: "+J),new URL(J,W)}let L={get action(){return g},get location(){return l(s,m)},listen(U){if(y)throw new Error("A history only accepts one active listener");return s.addEventListener(im,A),y=U,()=>{s.removeEventListener(im,A),y=null}},createHref(U){return u(s,U)},createURL:M,encodeLocation(U){let W=M(U);return{pathname:W.pathname,search:W.search,hash:W.hash}},push:N,replace:k,go(U){return m.go(U)}};return L}var cm;(function(l){l.data="data",l.deferred="deferred",l.redirect="redirect",l.error="error"})(cm||(cm={}));function my(l,u,r){return r===void 0&&(r="/"),vy(l,u,r)}function vy(l,u,r,o){let s=typeof u=="string"?ol(u):u,p=il(s.pathname||"/",r);if(p==null)return null;let m=Zm(l);hy(m);let g=null;for(let y=0;g==null&&y<m.length;++y){let v=zy(p);g=Oy(m[y],v)}return g}function Zm(l,u,r,o){u===void 0&&(u=[]),r===void 0&&(r=[]),o===void 0&&(o="");let s=(p,m,g)=>{let y={relativePath:g===void 0?p.path||"":g,caseSensitive:p.caseSensitive===!0,childrenIndex:m,route:p};y.relativePath.startsWith("/")&&(qt(y.relativePath.startsWith(o),'Absolute route path "'+y.relativePath+'" nested under path '+('"'+o+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),y.relativePath=y.relativePath.slice(o.length));let v=Qa([o,y.relativePath]),x=r.concat(y);p.children&&p.children.length>0&&(qt(p.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+v+'".')),Zm(p.children,u,x,v)),!(p.path==null&&!p.index)&&u.push({path:v,score:Ty(v,p.index),routesMeta:x})};return l.forEach((p,m)=>{var g;if(p.path===""||!((g=p.path)!=null&&g.includes("?")))s(p,m);else for(let y of Km(p.path))s(p,m,y)}),u}function Km(l){let u=l.split("/");if(u.length===0)return[];let[r,...o]=u,s=r.endsWith("?"),p=r.replace(/\?$/,"");if(o.length===0)return s?[p,""]:[p];let m=Km(o.join("/")),g=[];return g.push(...m.map(y=>y===""?p:[p,y].join("/"))),s&&g.push(...m),g.map(y=>l.startsWith("/")&&y===""?"/":y)}function hy(l){l.sort((u,r)=>u.score!==r.score?r.score-u.score:Ay(u.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}const gy=/^:[\w-]+$/,yy=3,xy=2,by=1,Sy=10,Ey=-2,om=l=>l==="*";function Ty(l,u){let r=l.split("/"),o=r.length;return r.some(om)&&(o+=Ey),u&&(o+=xy),r.filter(s=>!om(s)).reduce((s,p)=>s+(gy.test(p)?yy:p===""?by:Sy),o)}function Ay(l,u){return l.length===u.length&&l.slice(0,-1).every((o,s)=>o===u[s])?l[l.length-1]-u[u.length-1]:0}function Oy(l,u,r){let{routesMeta:o}=l,s={},p="/",m=[];for(let g=0;g<o.length;++g){let y=o[g],v=g===o.length-1,x=p==="/"?u:u.slice(p.length)||"/",A=qr({path:y.relativePath,caseSensitive:y.caseSensitive,end:v},x),N=y.route;if(!A)return null;Object.assign(s,A.params),m.push({params:s,pathname:Qa([p,A.pathname]),pathnameBase:jy(Qa([p,A.pathnameBase])),route:N}),A.pathnameBase!=="/"&&(p=Qa([p,A.pathnameBase]))}return m}function qr(l,u){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[r,o]=Ry(l.path,l.caseSensitive,l.end),s=u.match(r);if(!s)return null;let p=s[0],m=p.replace(/(.)\/+$/,"$1"),g=s.slice(1);return{params:o.reduce((v,x,A)=>{let{paramName:N,isOptional:k}=x;if(N==="*"){let L=g[A]||"";m=p.slice(0,p.length-L.length).replace(/(.)\/+$/,"$1")}const M=g[A];return k&&!M?v[N]=void 0:v[N]=(M||"").replace(/%2F/g,"/"),v},{}),pathname:p,pathnameBase:m,pattern:l}}function Ry(l,u,r){u===void 0&&(u=!1),r===void 0&&(r=!0),Vm(l==="*"||!l.endsWith("*")||l.endsWith("/*"),'Route path "'+l+'" will be treated as if it were '+('"'+l.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+l.replace(/\*$/,"/*")+'".'));let o=[],s="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,g,y)=>(o.push({paramName:g,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(o.push({paramName:"*"}),s+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?s+="\\/*$":l!==""&&l!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,u?void 0:"i"),o]}function zy(l){try{return l.split("/").map(u=>decodeURIComponent(u).replace(/\//g,"%2F")).join("/")}catch(u){return Vm(!1,'The URL path "'+l+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+u+").")),l}}function il(l,u){if(u==="/")return l;if(!l.toLowerCase().startsWith(u.toLowerCase()))return null;let r=u.endsWith("/")?u.length-1:u.length,o=l.charAt(r);return o&&o!=="/"?null:l.slice(r)||"/"}function _y(l,u){u===void 0&&(u="/");let{pathname:r,search:o="",hash:s=""}=typeof l=="string"?ol(l):l;return{pathname:r?r.startsWith("/")?r:Dy(r,u):u,search:Uy(o),hash:My(s)}}function Dy(l,u){let r=u.replace(/\/+$/,"").split("/");return l.split("/").forEach(s=>{s===".."?r.length>1&&r.pop():s!=="."&&r.push(s)}),r.length>1?r.join("/"):"/"}function Or(l,u,r,o){return"Cannot include a '"+l+"' character in a manually specified "+("`to."+u+"` field ["+JSON.stringify(o)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function wy(l){return l.filter((u,r)=>r===0||u.route.path&&u.route.path.length>0)}function ts(l,u){let r=wy(l);return u?r.map((o,s)=>s===r.length-1?o.pathname:o.pathnameBase):r.map(o=>o.pathnameBase)}function es(l,u,r,o){o===void 0&&(o=!1);let s;typeof l=="string"?s=ol(l):(s=gi({},l),qt(!s.pathname||!s.pathname.includes("?"),Or("?","pathname","search",s)),qt(!s.pathname||!s.pathname.includes("#"),Or("#","pathname","hash",s)),qt(!s.search||!s.search.includes("#"),Or("#","search","hash",s)));let p=l===""||s.pathname==="",m=p?"/":s.pathname,g;if(m==null)g=r;else{let A=u.length-1;if(!o&&m.startsWith("..")){let N=m.split("/");for(;N[0]==="..";)N.shift(),A-=1;s.pathname=N.join("/")}g=A>=0?u[A]:"/"}let y=_y(s,g),v=m&&m!=="/"&&m.endsWith("/"),x=(p||m===".")&&r.endsWith("/");return!y.pathname.endsWith("/")&&(v||x)&&(y.pathname+="/"),y}const Qa=l=>l.join("/").replace(/\/\/+/g,"/"),jy=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),Uy=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,My=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function Cy(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}const Jm=["post","put","patch","delete"];new Set(Jm);const Ny=["get",...Jm];new Set(Ny);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function yi(){return yi=Object.assign?Object.assign.bind():function(l){for(var u=1;u<arguments.length;u++){var r=arguments[u];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(l[o]=r[o])}return l},yi.apply(this,arguments)}const Wu=D.createContext(null),Fm=D.createContext(null),ma=D.createContext(null),Pu=D.createContext(null),Va=D.createContext({outlet:null,matches:[],isDataRoute:!1}),$m=D.createContext(null);function qy(l,u){let{relative:r}=u===void 0?{}:u;rl()||qt(!1);let{basename:o,navigator:s}=D.useContext(ma),{hash:p,pathname:m,search:g}=Iu(l,{relative:r}),y=m;return o!=="/"&&(y=m==="/"?o:Qa([o,m])),s.createHref({pathname:y,search:g,hash:p})}function rl(){return D.useContext(Pu)!=null}function sl(){return rl()||qt(!1),D.useContext(Pu).location}function Wm(l){D.useContext(ma).static||D.useLayoutEffect(l)}function Pm(){let{isDataRoute:l}=D.useContext(Va);return l?Fy():By()}function By(){rl()||qt(!1);let l=D.useContext(Wu),{basename:u,future:r,navigator:o}=D.useContext(ma),{matches:s}=D.useContext(Va),{pathname:p}=sl(),m=JSON.stringify(ts(s,r.v7_relativeSplatPath)),g=D.useRef(!1);return Wm(()=>{g.current=!0}),D.useCallback(function(v,x){if(x===void 0&&(x={}),!g.current)return;if(typeof v=="number"){o.go(v);return}let A=es(v,JSON.parse(m),p,x.relative==="path");l==null&&u!=="/"&&(A.pathname=A.pathname==="/"?u:Qa([u,A.pathname])),(x.replace?o.replace:o.push)(A,x.state,x)},[u,o,m,p,l])}function Iu(l,u){let{relative:r}=u===void 0?{}:u,{future:o}=D.useContext(ma),{matches:s}=D.useContext(Va),{pathname:p}=sl(),m=JSON.stringify(ts(s,o.v7_relativeSplatPath));return D.useMemo(()=>es(l,JSON.parse(m),p,r==="path"),[l,m,p,r])}function Hy(l,u){return Ly(l,u)}function Ly(l,u,r,o){rl()||qt(!1);let{navigator:s}=D.useContext(ma),{matches:p}=D.useContext(Va),m=p[p.length-1],g=m?m.params:{};m&&m.pathname;let y=m?m.pathnameBase:"/";m&&m.route;let v=sl(),x;if(u){var A;let U=typeof u=="string"?ol(u):u;y==="/"||(A=U.pathname)!=null&&A.startsWith(y)||qt(!1),x=U}else x=v;let N=x.pathname||"/",k=N;if(y!=="/"){let U=y.replace(/^\//,"").split("/");k="/"+N.replace(/^\//,"").split("/").slice(U.length).join("/")}let M=my(l,{pathname:k}),L=Qy(M&&M.map(U=>Object.assign({},U,{params:Object.assign({},g,U.params),pathname:Qa([y,s.encodeLocation?s.encodeLocation(U.pathname).pathname:U.pathname]),pathnameBase:U.pathnameBase==="/"?y:Qa([y,s.encodeLocation?s.encodeLocation(U.pathnameBase).pathname:U.pathnameBase])})),p,r,o);return u&&L?D.createElement(Pu.Provider,{value:{location:yi({pathname:"/",search:"",hash:"",state:null,key:"default"},x),navigationType:Xa.Pop}},L):L}function ky(){let l=Jy(),u=Cy(l)?l.status+" "+l.statusText:l instanceof Error?l.message:JSON.stringify(l),r=l instanceof Error?l.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return D.createElement(D.Fragment,null,D.createElement("h2",null,"Unexpected Application Error!"),D.createElement("h3",{style:{fontStyle:"italic"}},u),r?D.createElement("pre",{style:s},r):null,null)}const Yy=D.createElement(ky,null);class Gy extends D.Component{constructor(u){super(u),this.state={location:u.location,revalidation:u.revalidation,error:u.error}}static getDerivedStateFromError(u){return{error:u}}static getDerivedStateFromProps(u,r){return r.location!==u.location||r.revalidation!=="idle"&&u.revalidation==="idle"?{error:u.error,location:u.location,revalidation:u.revalidation}:{error:u.error!==void 0?u.error:r.error,location:r.location,revalidation:u.revalidation||r.revalidation}}componentDidCatch(u,r){console.error("React Router caught the following error during render",u,r)}render(){return this.state.error!==void 0?D.createElement(Va.Provider,{value:this.props.routeContext},D.createElement($m.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Xy(l){let{routeContext:u,match:r,children:o}=l,s=D.useContext(Wu);return s&&s.static&&s.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=r.route.id),D.createElement(Va.Provider,{value:u},o)}function Qy(l,u,r,o){var s;if(u===void 0&&(u=[]),r===void 0&&(r=null),o===void 0&&(o=null),l==null){var p;if(!r)return null;if(r.errors)l=r.matches;else if((p=o)!=null&&p.v7_partialHydration&&u.length===0&&!r.initialized&&r.matches.length>0)l=r.matches;else return null}let m=l,g=(s=r)==null?void 0:s.errors;if(g!=null){let x=m.findIndex(A=>A.route.id&&g?.[A.route.id]!==void 0);x>=0||qt(!1),m=m.slice(0,Math.min(m.length,x+1))}let y=!1,v=-1;if(r&&o&&o.v7_partialHydration)for(let x=0;x<m.length;x++){let A=m[x];if((A.route.HydrateFallback||A.route.hydrateFallbackElement)&&(v=x),A.route.id){let{loaderData:N,errors:k}=r,M=A.route.loader&&N[A.route.id]===void 0&&(!k||k[A.route.id]===void 0);if(A.route.lazy||M){y=!0,v>=0?m=m.slice(0,v+1):m=[m[0]];break}}}return m.reduceRight((x,A,N)=>{let k,M=!1,L=null,U=null;r&&(k=g&&A.route.id?g[A.route.id]:void 0,L=A.route.errorElement||Yy,y&&(v<0&&N===0?($y("route-fallback"),M=!0,U=null):v===N&&(M=!0,U=A.route.hydrateFallbackElement||null)));let W=u.concat(m.slice(0,N+1)),J=()=>{let Z;return k?Z=L:M?Z=U:A.route.Component?Z=D.createElement(A.route.Component,null):A.route.element?Z=A.route.element:Z=x,D.createElement(Xy,{match:A,routeContext:{outlet:x,matches:W,isDataRoute:r!=null},children:Z})};return r&&(A.route.ErrorBoundary||A.route.errorElement||N===0)?D.createElement(Gy,{location:r.location,revalidation:r.revalidation,component:L,error:k,children:J(),routeContext:{outlet:null,matches:W,isDataRoute:!0}}):J()},null)}var Im=function(l){return l.UseBlocker="useBlocker",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l}(Im||{}),tv=function(l){return l.UseBlocker="useBlocker",l.UseLoaderData="useLoaderData",l.UseActionData="useActionData",l.UseRouteError="useRouteError",l.UseNavigation="useNavigation",l.UseRouteLoaderData="useRouteLoaderData",l.UseMatches="useMatches",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l.UseRouteId="useRouteId",l}(tv||{});function Vy(l){let u=D.useContext(Wu);return u||qt(!1),u}function Zy(l){let u=D.useContext(Fm);return u||qt(!1),u}function Ky(l){let u=D.useContext(Va);return u||qt(!1),u}function ev(l){let u=Ky(),r=u.matches[u.matches.length-1];return r.route.id||qt(!1),r.route.id}function Jy(){var l;let u=D.useContext($m),r=Zy(),o=ev();return u!==void 0?u:(l=r.errors)==null?void 0:l[o]}function Fy(){let{router:l}=Vy(Im.UseNavigateStable),u=ev(tv.UseNavigateStable),r=D.useRef(!1);return Wm(()=>{r.current=!0}),D.useCallback(function(s,p){p===void 0&&(p={}),r.current&&(typeof s=="number"?l.navigate(s):l.navigate(s,yi({fromRouteId:u},p)))},[l,u])}const rm={};function $y(l,u,r){rm[l]||(rm[l]=!0)}function Wy(l,u){l?.v7_startTransition,l?.v7_relativeSplatPath}function sm(l){let{to:u,replace:r,state:o,relative:s}=l;rl()||qt(!1);let{future:p,static:m}=D.useContext(ma),{matches:g}=D.useContext(Va),{pathname:y}=sl(),v=Pm(),x=es(u,ts(g,p.v7_relativeSplatPath),y,s==="path"),A=JSON.stringify(x);return D.useEffect(()=>v(JSON.parse(A),{replace:r,state:o,relative:s}),[v,A,s,r,o]),null}function Ga(l){qt(!1)}function Py(l){let{basename:u="/",children:r=null,location:o,navigationType:s=Xa.Pop,navigator:p,static:m=!1,future:g}=l;rl()&&qt(!1);let y=u.replace(/^\/*/,"/"),v=D.useMemo(()=>({basename:y,navigator:p,static:m,future:yi({v7_relativeSplatPath:!1},g)}),[y,g,p,m]);typeof o=="string"&&(o=ol(o));let{pathname:x="/",search:A="",hash:N="",state:k=null,key:M="default"}=o,L=D.useMemo(()=>{let U=il(x,y);return U==null?null:{location:{pathname:U,search:A,hash:N,state:k,key:M},navigationType:s}},[y,x,A,N,k,M,s]);return L==null?null:D.createElement(ma.Provider,{value:v},D.createElement(Pu.Provider,{children:r,value:L}))}function Iy(l){let{children:u,location:r}=l;return Hy(Br(u),r)}new Promise(()=>{});function Br(l,u){u===void 0&&(u=[]);let r=[];return D.Children.forEach(l,(o,s)=>{if(!D.isValidElement(o))return;let p=[...u,s];if(o.type===D.Fragment){r.push.apply(r,Br(o.props.children,p));return}o.type!==Ga&&qt(!1),!o.props.index||!o.props.children||qt(!1);let m={id:o.props.id||p.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(m.children=Br(o.props.children,p)),r.push(m)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Vu(){return Vu=Object.assign?Object.assign.bind():function(l){for(var u=1;u<arguments.length;u++){var r=arguments[u];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(l[o]=r[o])}return l},Vu.apply(this,arguments)}function av(l,u){if(l==null)return{};var r={},o=Object.keys(l),s,p;for(p=0;p<o.length;p++)s=o[p],!(u.indexOf(s)>=0)&&(r[s]=l[s]);return r}function tx(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function ex(l,u){return l.button===0&&(!u||u==="_self")&&!tx(l)}const ax=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],nx=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],lx="6";try{window.__reactRouterVersion=lx}catch{}const ix=D.createContext({isTransitioning:!1}),ux="startTransition",fm=ly[ux];function cx(l){let{basename:u,children:r,future:o,window:s}=l,p=D.useRef();p.current==null&&(p.current=fy({window:s,v5Compat:!0}));let m=p.current,[g,y]=D.useState({action:m.action,location:m.location}),{v7_startTransition:v}=o||{},x=D.useCallback(A=>{v&&fm?fm(()=>y(A)):y(A)},[y,v]);return D.useLayoutEffect(()=>m.listen(x),[m,x]),D.useEffect(()=>Wy(o),[o]),D.createElement(Py,{basename:u,children:r,location:g.location,navigationType:g.action,navigator:m,future:o})}const ox=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",rx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nv=D.forwardRef(function(u,r){let{onClick:o,relative:s,reloadDocument:p,replace:m,state:g,target:y,to:v,preventScrollReset:x,viewTransition:A}=u,N=av(u,ax),{basename:k}=D.useContext(ma),M,L=!1;if(typeof v=="string"&&rx.test(v)&&(M=v,ox))try{let Z=new URL(window.location.href),tt=v.startsWith("//")?new URL(Z.protocol+v):new URL(v),Y=il(tt.pathname,k);tt.origin===Z.origin&&Y!=null?v=Y+tt.search+tt.hash:L=!0}catch{}let U=qy(v,{relative:s}),W=fx(v,{replace:m,state:g,target:y,preventScrollReset:x,relative:s,viewTransition:A});function J(Z){o&&o(Z),Z.defaultPrevented||W(Z)}return D.createElement("a",Vu({},N,{href:M||U,onClick:L||p?o:J,ref:r,target:y}))}),mi=D.forwardRef(function(u,r){let{"aria-current":o="page",caseSensitive:s=!1,className:p="",end:m=!1,style:g,to:y,viewTransition:v,children:x}=u,A=av(u,nx),N=Iu(y,{relative:A.relative}),k=sl(),M=D.useContext(Fm),{navigator:L,basename:U}=D.useContext(ma),W=M!=null&&px(N)&&v===!0,J=L.encodeLocation?L.encodeLocation(N).pathname:N.pathname,Z=k.pathname,tt=M&&M.navigation&&M.navigation.location?M.navigation.location.pathname:null;s||(Z=Z.toLowerCase(),tt=tt?tt.toLowerCase():null,J=J.toLowerCase()),tt&&U&&(tt=il(tt,U)||tt);const Y=J!=="/"&&J.endsWith("/")?J.length-1:J.length;let ft=Z===J||!m&&Z.startsWith(J)&&Z.charAt(Y)==="/",ot=tt!=null&&(tt===J||!m&&tt.startsWith(J)&&tt.charAt(J.length)==="/"),Tt={isActive:ft,isPending:ot,isTransitioning:W},xt=ft?o:void 0,Yt;typeof p=="function"?Yt=p(Tt):Yt=[p,ft?"active":null,ot?"pending":null,W?"transitioning":null].filter(Boolean).join(" ");let Gt=typeof g=="function"?g(Tt):g;return D.createElement(nv,Vu({},A,{"aria-current":xt,className:Yt,ref:r,style:Gt,to:y,viewTransition:v}),typeof x=="function"?x(Tt):x)});var Hr;(function(l){l.UseScrollRestoration="useScrollRestoration",l.UseSubmit="useSubmit",l.UseSubmitFetcher="useSubmitFetcher",l.UseFetcher="useFetcher",l.useViewTransitionState="useViewTransitionState"})(Hr||(Hr={}));var pm;(function(l){l.UseFetcher="useFetcher",l.UseFetchers="useFetchers",l.UseScrollRestoration="useScrollRestoration"})(pm||(pm={}));function sx(l){let u=D.useContext(Wu);return u||qt(!1),u}function fx(l,u){let{target:r,replace:o,state:s,preventScrollReset:p,relative:m,viewTransition:g}=u===void 0?{}:u,y=Pm(),v=sl(),x=Iu(l,{relative:m});return D.useCallback(A=>{if(ex(A,r)){A.preventDefault();let N=o!==void 0?o:Qu(v)===Qu(x);y(l,{replace:N,state:s,preventScrollReset:p,relative:m,viewTransition:g})}},[v,y,x,o,s,r,l,p,m,g])}function px(l,u){u===void 0&&(u={});let r=D.useContext(ix);r==null&&qt(!1);let{basename:o}=sx(Hr.useViewTransitionState),s=Iu(l,{relative:u.relative});if(!r.isTransitioning)return!1;let p=il(r.currentLocation.pathname,o)||r.currentLocation.pathname,m=il(r.nextLocation.pathname,o)||r.nextLocation.pathname;return qr(s.pathname,m)!=null||qr(s.pathname,p)!=null}function dx(){return B.jsx("header",{className:"navbar",children:B.jsxs("div",{className:"navbar-inner",children:[B.jsxs(nv,{to:"/",className:"nav-link",style:{fontWeight:800,fontSize:18},children:["🎓 ",B.jsx("span",{style:{color:"var(--color-accent)"},children:"Scholar"}),"AI"]}),B.jsxs("nav",{style:{display:"flex",gap:8},children:[B.jsx(mi,{to:"/materials",className:({isActive:l})=>`nav-link${l?" active":""}`,children:"Materials"}),B.jsx(mi,{to:"/chat",className:({isActive:l})=>`nav-link${l?" active":""}`,children:"Chat"}),B.jsx(mi,{to:"/quiz",className:({isActive:l})=>`nav-link${l?" active":""}`,children:"Quiz"}),B.jsx(mi,{to:"/dashboard",className:({isActive:l})=>`nav-link${l?" active":""}`,children:"Dashboard"}),B.jsx(mi,{to:"/parental-controls",className:({isActive:l})=>`nav-link${l?" active":""}`,children:"Parental Controls"})]})]})})}function ul({children:l}){return B.jsxs("div",{style:{minHeight:"100vh"},children:[B.jsx(dx,{}),B.jsx("main",{className:"container",children:l}),B.jsx("footer",{className:"footer",children:B.jsx("p",{children:"ScholarAI - Safe AI Study Companion for Ages 7-18"})})]})}var Rr={exports:{}},zr,dm;function mx(){if(dm)return zr;dm=1;var l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return zr=l,zr}var _r,mm;function vx(){if(mm)return _r;mm=1;var l=mx();function u(){}function r(){}return r.resetWarningCache=u,_r=function(){function o(m,g,y,v,x,A){if(A!==l){var N=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw N.name="Invariant Violation",N}}o.isRequired=o;function s(){return o}var p={array:o,bigint:o,bool:o,func:o,number:o,object:o,string:o,symbol:o,any:o,arrayOf:s,element:o,elementType:o,instanceOf:s,node:o,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:r,resetWarningCache:u};return p.PropTypes=p,p},_r}var vm;function hx(){return vm||(vm=1,Rr.exports=vx()()),Rr.exports}var gx=hx();const Dt=Pr(gx);function hn(l,u,r,o){function s(p){return p instanceof r?p:new r(function(m){m(p)})}return new(r||(r=Promise))(function(p,m){function g(x){try{v(o.next(x))}catch(A){m(A)}}function y(x){try{v(o.throw(x))}catch(A){m(A)}}function v(x){x.done?p(x.value):s(x.value).then(g,y)}v((o=o.apply(l,u||[])).next())})}const yx=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function cl(l,u,r){const o=xx(l),{webkitRelativePath:s}=l,p=typeof u=="string"?u:typeof s=="string"&&s.length>0?s:`./${l.name}`;return typeof o.path!="string"&&hm(o,"path",p),hm(o,"relativePath",p),o}function xx(l){const{name:u}=l;if(u&&u.lastIndexOf(".")!==-1&&!l.type){const o=u.split(".").pop().toLowerCase(),s=yx.get(o);s&&Object.defineProperty(l,"type",{value:s,writable:!1,configurable:!1,enumerable:!0})}return l}function hm(l,u,r){Object.defineProperty(l,u,{value:r,writable:!1,configurable:!1,enumerable:!0})}const bx=[".DS_Store","Thumbs.db"];function Sx(l){return hn(this,void 0,void 0,function*(){return Zu(l)&&Ex(l.dataTransfer)?Rx(l.dataTransfer,l.type):Tx(l)?Ax(l):Array.isArray(l)&&l.every(u=>"getFile"in u&&typeof u.getFile=="function")?Ox(l):[]})}function Ex(l){return Zu(l)}function Tx(l){return Zu(l)&&Zu(l.target)}function Zu(l){return typeof l=="object"&&l!==null}function Ax(l){return Lr(l.target.files).map(u=>cl(u))}function Ox(l){return hn(this,void 0,void 0,function*(){return(yield Promise.all(l.map(r=>r.getFile()))).map(r=>cl(r))})}function Rx(l,u){return hn(this,void 0,void 0,function*(){if(l.items){const r=Lr(l.items).filter(s=>s.kind==="file");if(u!=="drop")return r;const o=yield Promise.all(r.map(zx));return gm(lv(o))}return gm(Lr(l.files).map(r=>cl(r)))})}function gm(l){return l.filter(u=>bx.indexOf(u.name)===-1)}function Lr(l){if(l===null)return[];const u=[];for(let r=0;r<l.length;r++){const o=l[r];u.push(o)}return u}function zx(l){if(typeof l.webkitGetAsEntry!="function")return ym(l);const u=l.webkitGetAsEntry();return u&&u.isDirectory?iv(u):ym(l,u)}function lv(l){return l.reduce((u,r)=>[...u,...Array.isArray(r)?lv(r):[r]],[])}function ym(l,u){return hn(this,void 0,void 0,function*(){var r;if(globalThis.isSecureContext&&typeof l.getAsFileSystemHandle=="function"){const p=yield l.getAsFileSystemHandle();if(p===null)throw new Error(`${l} is not a File`);if(p!==void 0){const m=yield p.getFile();return m.handle=p,cl(m)}}const o=l.getAsFile();if(!o)throw new Error(`${l} is not a File`);return cl(o,(r=u?.fullPath)!==null&&r!==void 0?r:void 0)})}function _x(l){return hn(this,void 0,void 0,function*(){return l.isDirectory?iv(l):Dx(l)})}function iv(l){const u=l.createReader();return new Promise((r,o)=>{const s=[];function p(){u.readEntries(m=>hn(this,void 0,void 0,function*(){if(m.length){const g=Promise.all(m.map(_x));s.push(g),p()}else try{const g=yield Promise.all(s);r(g)}catch(g){o(g)}}),m=>{o(m)})}p()})}function Dx(l){return hn(this,void 0,void 0,function*(){return new Promise((u,r)=>{l.file(o=>{const s=cl(o,l.fullPath);u(s)},o=>{r(o)})})})}var Lu={},xm;function wx(){return xm||(xm=1,Lu.__esModule=!0,Lu.default=function(l,u){if(l&&u){var r=Array.isArray(u)?u:u.split(",");if(r.length===0)return!0;var o=l.name||"",s=(l.type||"").toLowerCase(),p=s.replace(/\/.*$/,"");return r.some(function(m){var g=m.trim().toLowerCase();return g.charAt(0)==="."?o.toLowerCase().endsWith(g):g.endsWith("/*")?p===g.replace(/\/.*$/,""):s===g})}return!0}),Lu}var jx=wx();const Dr=Pr(jx);function bm(l){return Cx(l)||Mx(l)||cv(l)||Ux()}function Ux(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mx(l){if(typeof Symbol<"u"&&l[Symbol.iterator]!=null||l["@@iterator"]!=null)return Array.from(l)}function Cx(l){if(Array.isArray(l))return kr(l)}function Sm(l,u){var r=Object.keys(l);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(l);u&&(o=o.filter(function(s){return Object.getOwnPropertyDescriptor(l,s).enumerable})),r.push.apply(r,o)}return r}function Em(l){for(var u=1;u<arguments.length;u++){var r=arguments[u]!=null?arguments[u]:{};u%2?Sm(Object(r),!0).forEach(function(o){uv(l,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(r)):Sm(Object(r)).forEach(function(o){Object.defineProperty(l,o,Object.getOwnPropertyDescriptor(r,o))})}return l}function uv(l,u,r){return u in l?Object.defineProperty(l,u,{value:r,enumerable:!0,configurable:!0,writable:!0}):l[u]=r,l}function xi(l,u){return Bx(l)||qx(l,u)||cv(l,u)||Nx()}function Nx(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cv(l,u){if(l){if(typeof l=="string")return kr(l,u);var r=Object.prototype.toString.call(l).slice(8,-1);if(r==="Object"&&l.constructor&&(r=l.constructor.name),r==="Map"||r==="Set")return Array.from(l);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kr(l,u)}}function kr(l,u){(u==null||u>l.length)&&(u=l.length);for(var r=0,o=new Array(u);r<u;r++)o[r]=l[r];return o}function qx(l,u){var r=l==null?null:typeof Symbol<"u"&&l[Symbol.iterator]||l["@@iterator"];if(r!=null){var o=[],s=!0,p=!1,m,g;try{for(r=r.call(l);!(s=(m=r.next()).done)&&(o.push(m.value),!(u&&o.length===u));s=!0);}catch(y){p=!0,g=y}finally{try{!s&&r.return!=null&&r.return()}finally{if(p)throw g}}return o}}function Bx(l){if(Array.isArray(l))return l}var Hx=typeof Dr=="function"?Dr:Dr.default,Lx="file-invalid-type",kx="file-too-large",Yx="file-too-small",Gx="too-many-files",Xx=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=u.split(","),o=r.length>1?"one of ".concat(r.join(", ")):r[0];return{code:Lx,message:"File type must be ".concat(o)}},Tm=function(u){return{code:kx,message:"File is larger than ".concat(u," ").concat(u===1?"byte":"bytes")}},Am=function(u){return{code:Yx,message:"File is smaller than ".concat(u," ").concat(u===1?"byte":"bytes")}},Qx={code:Gx,message:"Too many files"};function ov(l,u){var r=l.type==="application/x-moz-file"||Hx(l,u);return[r,r?null:Xx(u)]}function rv(l,u,r){if(pn(l.size))if(pn(u)&&pn(r)){if(l.size>r)return[!1,Tm(r)];if(l.size<u)return[!1,Am(u)]}else{if(pn(u)&&l.size<u)return[!1,Am(u)];if(pn(r)&&l.size>r)return[!1,Tm(r)]}return[!0,null]}function pn(l){return l!=null}function Vx(l){var u=l.files,r=l.accept,o=l.minSize,s=l.maxSize,p=l.multiple,m=l.maxFiles,g=l.validator;return!p&&u.length>1||p&&m>=1&&u.length>m?!1:u.every(function(y){var v=ov(y,r),x=xi(v,1),A=x[0],N=rv(y,o,s),k=xi(N,1),M=k[0],L=g?g(y):null;return A&&M&&!L})}function Ku(l){return typeof l.isPropagationStopped=="function"?l.isPropagationStopped():typeof l.cancelBubble<"u"?l.cancelBubble:!1}function ku(l){return l.dataTransfer?Array.prototype.some.call(l.dataTransfer.types,function(u){return u==="Files"||u==="application/x-moz-file"}):!!l.target&&!!l.target.files}function Om(l){l.preventDefault()}function Zx(l){return l.indexOf("MSIE")!==-1||l.indexOf("Trident/")!==-1}function Kx(l){return l.indexOf("Edge/")!==-1}function Jx(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Zx(l)||Kx(l)}function Je(){for(var l=arguments.length,u=new Array(l),r=0;r<l;r++)u[r]=arguments[r];return function(o){for(var s=arguments.length,p=new Array(s>1?s-1:0),m=1;m<s;m++)p[m-1]=arguments[m];return u.some(function(g){return!Ku(o)&&g&&g.apply(void 0,[o].concat(p)),Ku(o)})}}function Fx(){return"showOpenFilePicker"in window}function $x(l){if(pn(l)){var u=Object.entries(l).filter(function(r){var o=xi(r,2),s=o[0],p=o[1],m=!0;return sv(s)||(console.warn('Skipped "'.concat(s,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),m=!1),(!Array.isArray(p)||!p.every(fv))&&(console.warn('Skipped "'.concat(s,'" because an invalid file extension was provided.')),m=!1),m}).reduce(function(r,o){var s=xi(o,2),p=s[0],m=s[1];return Em(Em({},r),{},uv({},p,m))},{});return[{description:"Files",accept:u}]}return l}function Wx(l){if(pn(l))return Object.entries(l).reduce(function(u,r){var o=xi(r,2),s=o[0],p=o[1];return[].concat(bm(u),[s],bm(p))},[]).filter(function(u){return sv(u)||fv(u)}).join(",")}function Px(l){return l instanceof DOMException&&(l.name==="AbortError"||l.code===l.ABORT_ERR)}function Ix(l){return l instanceof DOMException&&(l.name==="SecurityError"||l.code===l.SECURITY_ERR)}function sv(l){return l==="audio/*"||l==="video/*"||l==="image/*"||l==="text/*"||l==="application/*"||/\w+\/[-+.\w]+/g.test(l)}function fv(l){return/^.*\.[\w]+$/.test(l)}var t0=["children"],e0=["open"],a0=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],n0=["refKey","onChange","onClick"];function l0(l){return c0(l)||u0(l)||pv(l)||i0()}function i0(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function u0(l){if(typeof Symbol<"u"&&l[Symbol.iterator]!=null||l["@@iterator"]!=null)return Array.from(l)}function c0(l){if(Array.isArray(l))return Yr(l)}function wr(l,u){return s0(l)||r0(l,u)||pv(l,u)||o0()}function o0(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pv(l,u){if(l){if(typeof l=="string")return Yr(l,u);var r=Object.prototype.toString.call(l).slice(8,-1);if(r==="Object"&&l.constructor&&(r=l.constructor.name),r==="Map"||r==="Set")return Array.from(l);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yr(l,u)}}function Yr(l,u){(u==null||u>l.length)&&(u=l.length);for(var r=0,o=new Array(u);r<u;r++)o[r]=l[r];return o}function r0(l,u){var r=l==null?null:typeof Symbol<"u"&&l[Symbol.iterator]||l["@@iterator"];if(r!=null){var o=[],s=!0,p=!1,m,g;try{for(r=r.call(l);!(s=(m=r.next()).done)&&(o.push(m.value),!(u&&o.length===u));s=!0);}catch(y){p=!0,g=y}finally{try{!s&&r.return!=null&&r.return()}finally{if(p)throw g}}return o}}function s0(l){if(Array.isArray(l))return l}function Rm(l,u){var r=Object.keys(l);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(l);u&&(o=o.filter(function(s){return Object.getOwnPropertyDescriptor(l,s).enumerable})),r.push.apply(r,o)}return r}function Nt(l){for(var u=1;u<arguments.length;u++){var r=arguments[u]!=null?arguments[u]:{};u%2?Rm(Object(r),!0).forEach(function(o){Gr(l,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(r)):Rm(Object(r)).forEach(function(o){Object.defineProperty(l,o,Object.getOwnPropertyDescriptor(r,o))})}return l}function Gr(l,u,r){return u in l?Object.defineProperty(l,u,{value:r,enumerable:!0,configurable:!0,writable:!0}):l[u]=r,l}function Ju(l,u){if(l==null)return{};var r=f0(l,u),o,s;if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(l);for(s=0;s<p.length;s++)o=p[s],!(u.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(l,o)&&(r[o]=l[o])}return r}function f0(l,u){if(l==null)return{};var r={},o=Object.keys(l),s,p;for(p=0;p<o.length;p++)s=o[p],!(u.indexOf(s)>=0)&&(r[s]=l[s]);return r}var as=D.forwardRef(function(l,u){var r=l.children,o=Ju(l,t0),s=mv(o),p=s.open,m=Ju(s,e0);return D.useImperativeHandle(u,function(){return{open:p}},[p]),Xm.createElement(D.Fragment,null,r(Nt(Nt({},m),{},{open:p})))});as.displayName="Dropzone";var dv={disabled:!1,getFilesFromEvent:Sx,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};as.defaultProps=dv;as.propTypes={children:Dt.func,accept:Dt.objectOf(Dt.arrayOf(Dt.string)),multiple:Dt.bool,preventDropOnDocument:Dt.bool,noClick:Dt.bool,noKeyboard:Dt.bool,noDrag:Dt.bool,noDragEventsBubbling:Dt.bool,minSize:Dt.number,maxSize:Dt.number,maxFiles:Dt.number,disabled:Dt.bool,getFilesFromEvent:Dt.func,onFileDialogCancel:Dt.func,onFileDialogOpen:Dt.func,useFsAccessApi:Dt.bool,autoFocus:Dt.bool,onDragEnter:Dt.func,onDragLeave:Dt.func,onDragOver:Dt.func,onDrop:Dt.func,onDropAccepted:Dt.func,onDropRejected:Dt.func,onError:Dt.func,validator:Dt.func};var Xr={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function mv(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=Nt(Nt({},dv),l),r=u.accept,o=u.disabled,s=u.getFilesFromEvent,p=u.maxSize,m=u.minSize,g=u.multiple,y=u.maxFiles,v=u.onDragEnter,x=u.onDragLeave,A=u.onDragOver,N=u.onDrop,k=u.onDropAccepted,M=u.onDropRejected,L=u.onFileDialogCancel,U=u.onFileDialogOpen,W=u.useFsAccessApi,J=u.autoFocus,Z=u.preventDropOnDocument,tt=u.noClick,Y=u.noKeyboard,ft=u.noDrag,ot=u.noDragEventsBubbling,Tt=u.onError,xt=u.validator,Yt=D.useMemo(function(){return Wx(r)},[r]),Gt=D.useMemo(function(){return $x(r)},[r]),Bt=D.useMemo(function(){return typeof U=="function"?U:zm},[U]),Oe=D.useMemo(function(){return typeof L=="function"?L:zm},[L]),Mt=D.useRef(null),bt=D.useRef(null),w=D.useReducer(p0,Xr),X=wr(w,2),P=X[0],ut=X[1],b=P.isFocused,H=P.isFileDialogActive,Q=D.useRef(typeof window<"u"&&window.isSecureContext&&W&&Fx()),G=function(){!Q.current&&H&&setTimeout(function(){if(bt.current){var at=bt.current.files;at.length||(ut({type:"closeDialog"}),Oe())}},300)};D.useEffect(function(){return window.addEventListener("focus",G,!1),function(){window.removeEventListener("focus",G,!1)}},[bt,H,Oe,Q]);var F=D.useRef([]),mt=function(at){Mt.current&&Mt.current.contains(at.target)||(at.preventDefault(),F.current=[])};D.useEffect(function(){return Z&&(document.addEventListener("dragover",Om,!1),document.addEventListener("drop",mt,!1)),function(){Z&&(document.removeEventListener("dragover",Om),document.removeEventListener("drop",mt))}},[Mt,Z]),D.useEffect(function(){return!o&&J&&Mt.current&&Mt.current.focus(),function(){}},[Mt,J,o]);var I=D.useCallback(function(V){Tt?Tt(V):console.error(V)},[Tt]),ie=D.useCallback(function(V){V.preventDefault(),V.persist(),Ka(V),F.current=[].concat(l0(F.current),[V.target]),ku(V)&&Promise.resolve(s(V)).then(function(at){if(!(Ku(V)&&!ot)){var wt=at.length,ht=wt>0&&Vx({files:at,accept:Yt,minSize:m,maxSize:p,multiple:g,maxFiles:y,validator:xt}),Ht=wt>0&&!ht;ut({isDragAccept:ht,isDragReject:Ht,isDragActive:!0,type:"setDraggedFiles"}),v&&v(V)}}).catch(function(at){return I(at)})},[s,v,I,ot,Yt,m,p,g,y,xt]),zt=D.useCallback(function(V){V.preventDefault(),V.persist(),Ka(V);var at=ku(V);if(at&&V.dataTransfer)try{V.dataTransfer.dropEffect="copy"}catch{}return at&&A&&A(V),!1},[A,ot]),Ne=D.useCallback(function(V){V.preventDefault(),V.persist(),Ka(V);var at=F.current.filter(function(ht){return Mt.current&&Mt.current.contains(ht)}),wt=at.indexOf(V.target);wt!==-1&&at.splice(wt,1),F.current=at,!(at.length>0)&&(ut({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),ku(V)&&x&&x(V))},[Mt,x,ot]),va=D.useCallback(function(V,at){var wt=[],ht=[];V.forEach(function(Ht){var Jt=ov(Ht,Yt),ha=wr(Jt,2),ml=ha[0],vl=ha[1],ga=rv(Ht,m,p),We=wr(ga,2),qe=We[0],ya=We[1],Pe=xt?xt(Ht):null;if(ml&&qe&&!Pe)wt.push(Ht);else{var hl=[vl,ya];Pe&&(hl=hl.concat(Pe)),ht.push({file:Ht,errors:hl.filter(function(_i){return _i})})}}),(!g&&wt.length>1||g&&y>=1&&wt.length>y)&&(wt.forEach(function(Ht){ht.push({file:Ht,errors:[Qx]})}),wt.splice(0)),ut({acceptedFiles:wt,fileRejections:ht,isDragReject:ht.length>0,type:"setFiles"}),N&&N(wt,ht,at),ht.length>0&&M&&M(ht,at),wt.length>0&&k&&k(wt,at)},[ut,g,Yt,m,p,y,N,k,M,xt]),$e=D.useCallback(function(V){V.preventDefault(),V.persist(),Ka(V),F.current=[],ku(V)&&Promise.resolve(s(V)).then(function(at){Ku(V)&&!ot||va(at,V)}).catch(function(at){return I(at)}),ut({type:"reset"})},[s,va,I,ot]),Ge=D.useCallback(function(){if(Q.current){ut({type:"openDialog"}),Bt();var V={multiple:g,types:Gt};window.showOpenFilePicker(V).then(function(at){return s(at)}).then(function(at){va(at,null),ut({type:"closeDialog"})}).catch(function(at){Px(at)?(Oe(at),ut({type:"closeDialog"})):Ix(at)?(Q.current=!1,bt.current?(bt.current.value=null,bt.current.click()):I(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):I(at)});return}bt.current&&(ut({type:"openDialog"}),Bt(),bt.current.value=null,bt.current.click())},[ut,Bt,Oe,W,va,I,Gt,g]),gn=D.useCallback(function(V){!Mt.current||!Mt.current.isEqualNode(V.target)||(V.key===" "||V.key==="Enter"||V.keyCode===32||V.keyCode===13)&&(V.preventDefault(),Ge())},[Mt,Ge]),yn=D.useCallback(function(){ut({type:"focus"})},[]),Oi=D.useCallback(function(){ut({type:"blur"})},[]),Ri=D.useCallback(function(){tt||(Jx()?setTimeout(Ge,0):Ge())},[tt,Ge]),ee=function(at){return o?null:at},dl=function(at){return Y?null:ee(at)},Za=function(at){return ft?null:ee(at)},Ka=function(at){ot&&at.stopPropagation()},xn=D.useMemo(function(){return function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},at=V.refKey,wt=at===void 0?"ref":at,ht=V.role,Ht=V.onKeyDown,Jt=V.onFocus,ha=V.onBlur,ml=V.onClick,vl=V.onDragEnter,ga=V.onDragOver,We=V.onDragLeave,qe=V.onDrop,ya=Ju(V,a0);return Nt(Nt(Gr({onKeyDown:dl(Je(Ht,gn)),onFocus:dl(Je(Jt,yn)),onBlur:dl(Je(ha,Oi)),onClick:ee(Je(ml,Ri)),onDragEnter:Za(Je(vl,ie)),onDragOver:Za(Je(ga,zt)),onDragLeave:Za(Je(We,Ne)),onDrop:Za(Je(qe,$e)),role:typeof ht=="string"&&ht!==""?ht:"presentation"},wt,Mt),!o&&!Y?{tabIndex:0}:{}),ya)}},[Mt,gn,yn,Oi,Ri,ie,zt,Ne,$e,Y,ft,o]),uc=D.useCallback(function(V){V.stopPropagation()},[]),zi=D.useMemo(function(){return function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},at=V.refKey,wt=at===void 0?"ref":at,ht=V.onChange,Ht=V.onClick,Jt=Ju(V,n0),ha=Gr({accept:Yt,multiple:g,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:ee(Je(ht,$e)),onClick:ee(Je(Ht,uc)),tabIndex:-1},wt,bt);return Nt(Nt({},ha),Jt)}},[bt,r,g,$e,o]);return Nt(Nt({},P),{},{isFocused:b&&!o,getRootProps:xn,getInputProps:zi,rootRef:Mt,inputRef:bt,open:ee(Ge)})}function p0(l,u){switch(u.type){case"focus":return Nt(Nt({},l),{},{isFocused:!0});case"blur":return Nt(Nt({},l),{},{isFocused:!1});case"openDialog":return Nt(Nt({},Xr),{},{isFileDialogActive:!0});case"closeDialog":return Nt(Nt({},l),{},{isFileDialogActive:!1});case"setDraggedFiles":return Nt(Nt({},l),{},{isDragActive:u.isDragActive,isDragAccept:u.isDragAccept,isDragReject:u.isDragReject});case"setFiles":return Nt(Nt({},l),{},{acceptedFiles:u.acceptedFiles,fileRejections:u.fileRejections,isDragReject:u.isDragReject});case"reset":return Nt({},Xr);default:return l}}function zm(){}function vv(l,u){return function(){return l.apply(u,arguments)}}const{toString:d0}=Object.prototype,{getPrototypeOf:ns}=Object,{iterator:tc,toStringTag:hv}=Symbol,ec=(l=>u=>{const r=d0.call(u);return l[r]||(l[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Ye=l=>(l=l.toLowerCase(),u=>ec(u)===l),ac=l=>u=>typeof u===l,{isArray:fl}=Array,bi=ac("undefined");function Si(l){return l!==null&&!bi(l)&&l.constructor!==null&&!bi(l.constructor)&&he(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const gv=Ye("ArrayBuffer");function m0(l){let u;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?u=ArrayBuffer.isView(l):u=l&&l.buffer&&gv(l.buffer),u}const v0=ac("string"),he=ac("function"),yv=ac("number"),Ei=l=>l!==null&&typeof l=="object",h0=l=>l===!0||l===!1,Yu=l=>{if(ec(l)!=="object")return!1;const u=ns(l);return(u===null||u===Object.prototype||Object.getPrototypeOf(u)===null)&&!(hv in l)&&!(tc in l)},g0=l=>{if(!Ei(l)||Si(l))return!1;try{return Object.keys(l).length===0&&Object.getPrototypeOf(l)===Object.prototype}catch{return!1}},y0=Ye("Date"),x0=Ye("File"),b0=Ye("Blob"),S0=Ye("FileList"),E0=l=>Ei(l)&&he(l.pipe),T0=l=>{let u;return l&&(typeof FormData=="function"&&l instanceof FormData||he(l.append)&&((u=ec(l))==="formdata"||u==="object"&&he(l.toString)&&l.toString()==="[object FormData]"))},A0=Ye("URLSearchParams"),[O0,R0,z0,_0]=["ReadableStream","Request","Response","Headers"].map(Ye),D0=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ti(l,u,{allOwnKeys:r=!1}={}){if(l===null||typeof l>"u")return;let o,s;if(typeof l!="object"&&(l=[l]),fl(l))for(o=0,s=l.length;o<s;o++)u.call(null,l[o],o,l);else{if(Si(l))return;const p=r?Object.getOwnPropertyNames(l):Object.keys(l),m=p.length;let g;for(o=0;o<m;o++)g=p[o],u.call(null,l[g],g,l)}}function xv(l,u){if(Si(l))return null;u=u.toLowerCase();const r=Object.keys(l);let o=r.length,s;for(;o-- >0;)if(s=r[o],u===s.toLowerCase())return s;return null}const dn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,bv=l=>!bi(l)&&l!==dn;function Qr(){const{caseless:l}=bv(this)&&this||{},u={},r=(o,s)=>{const p=l&&xv(u,s)||s;Yu(u[p])&&Yu(o)?u[p]=Qr(u[p],o):Yu(o)?u[p]=Qr({},o):fl(o)?u[p]=o.slice():u[p]=o};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&Ti(arguments[o],r);return u}const w0=(l,u,r,{allOwnKeys:o}={})=>(Ti(u,(s,p)=>{r&&he(s)?l[p]=vv(s,r):l[p]=s},{allOwnKeys:o}),l),j0=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),U0=(l,u,r,o)=>{l.prototype=Object.create(u.prototype,o),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:u.prototype}),r&&Object.assign(l.prototype,r)},M0=(l,u,r,o)=>{let s,p,m;const g={};if(u=u||{},l==null)return u;do{for(s=Object.getOwnPropertyNames(l),p=s.length;p-- >0;)m=s[p],(!o||o(m,l,u))&&!g[m]&&(u[m]=l[m],g[m]=!0);l=r!==!1&&ns(l)}while(l&&(!r||r(l,u))&&l!==Object.prototype);return u},C0=(l,u,r)=>{l=String(l),(r===void 0||r>l.length)&&(r=l.length),r-=u.length;const o=l.indexOf(u,r);return o!==-1&&o===r},N0=l=>{if(!l)return null;if(fl(l))return l;let u=l.length;if(!yv(u))return null;const r=new Array(u);for(;u-- >0;)r[u]=l[u];return r},q0=(l=>u=>l&&u instanceof l)(typeof Uint8Array<"u"&&ns(Uint8Array)),B0=(l,u)=>{const o=(l&&l[tc]).call(l);let s;for(;(s=o.next())&&!s.done;){const p=s.value;u.call(l,p[0],p[1])}},H0=(l,u)=>{let r;const o=[];for(;(r=l.exec(u))!==null;)o.push(r);return o},L0=Ye("HTMLFormElement"),k0=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,o,s){return o.toUpperCase()+s}),_m=(({hasOwnProperty:l})=>(u,r)=>l.call(u,r))(Object.prototype),Y0=Ye("RegExp"),Sv=(l,u)=>{const r=Object.getOwnPropertyDescriptors(l),o={};Ti(r,(s,p)=>{let m;(m=u(s,p,l))!==!1&&(o[p]=m||s)}),Object.defineProperties(l,o)},G0=l=>{Sv(l,(u,r)=>{if(he(l)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const o=l[r];if(he(o)){if(u.enumerable=!1,"writable"in u){u.writable=!1;return}u.set||(u.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},X0=(l,u)=>{const r={},o=s=>{s.forEach(p=>{r[p]=!0})};return fl(l)?o(l):o(String(l).split(u)),r},Q0=()=>{},V0=(l,u)=>l!=null&&Number.isFinite(l=+l)?l:u;function Z0(l){return!!(l&&he(l.append)&&l[hv]==="FormData"&&l[tc])}const K0=l=>{const u=new Array(10),r=(o,s)=>{if(Ei(o)){if(u.indexOf(o)>=0)return;if(Si(o))return o;if(!("toJSON"in o)){u[s]=o;const p=fl(o)?[]:{};return Ti(o,(m,g)=>{const y=r(m,s+1);!bi(y)&&(p[g]=y)}),u[s]=void 0,p}}return o};return r(l,0)},J0=Ye("AsyncFunction"),F0=l=>l&&(Ei(l)||he(l))&&he(l.then)&&he(l.catch),Ev=((l,u)=>l?setImmediate:u?((r,o)=>(dn.addEventListener("message",({source:s,data:p})=>{s===dn&&p===r&&o.length&&o.shift()()},!1),s=>{o.push(s),dn.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",he(dn.postMessage)),$0=typeof queueMicrotask<"u"?queueMicrotask.bind(dn):typeof process<"u"&&process.nextTick||Ev,W0=l=>l!=null&&he(l[tc]),_={isArray:fl,isArrayBuffer:gv,isBuffer:Si,isFormData:T0,isArrayBufferView:m0,isString:v0,isNumber:yv,isBoolean:h0,isObject:Ei,isPlainObject:Yu,isEmptyObject:g0,isReadableStream:O0,isRequest:R0,isResponse:z0,isHeaders:_0,isUndefined:bi,isDate:y0,isFile:x0,isBlob:b0,isRegExp:Y0,isFunction:he,isStream:E0,isURLSearchParams:A0,isTypedArray:q0,isFileList:S0,forEach:Ti,merge:Qr,extend:w0,trim:D0,stripBOM:j0,inherits:U0,toFlatObject:M0,kindOf:ec,kindOfTest:Ye,endsWith:C0,toArray:N0,forEachEntry:B0,matchAll:H0,isHTMLForm:L0,hasOwnProperty:_m,hasOwnProp:_m,reduceDescriptors:Sv,freezeMethods:G0,toObjectSet:X0,toCamelCase:k0,noop:Q0,toFiniteNumber:V0,findKey:xv,global:dn,isContextDefined:bv,isSpecCompliantForm:Z0,toJSONObject:K0,isAsyncFn:J0,isThenable:F0,setImmediate:Ev,asap:$0,isIterable:W0};function it(l,u,r,o,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",u&&(this.code=u),r&&(this.config=r),o&&(this.request=o),s&&(this.response=s,this.status=s.status?s.status:null)}_.inherits(it,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:_.toJSONObject(this.config),code:this.code,status:this.status}}});const Tv=it.prototype,Av={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{Av[l]={value:l}});Object.defineProperties(it,Av);Object.defineProperty(Tv,"isAxiosError",{value:!0});it.from=(l,u,r,o,s,p)=>{const m=Object.create(Tv);return _.toFlatObject(l,m,function(y){return y!==Error.prototype},g=>g!=="isAxiosError"),it.call(m,l.message,u,r,o,s),m.cause=l,m.name=l.name,p&&Object.assign(m,p),m};const P0=null;function Vr(l){return _.isPlainObject(l)||_.isArray(l)}function Ov(l){return _.endsWith(l,"[]")?l.slice(0,-2):l}function Dm(l,u,r){return l?l.concat(u).map(function(s,p){return s=Ov(s),!r&&p?"["+s+"]":s}).join(r?".":""):u}function I0(l){return _.isArray(l)&&!l.some(Vr)}const tb=_.toFlatObject(_,{},null,function(u){return/^is[A-Z]/.test(u)});function nc(l,u,r){if(!_.isObject(l))throw new TypeError("target must be an object");u=u||new FormData,r=_.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(L,U){return!_.isUndefined(U[L])});const o=r.metaTokens,s=r.visitor||x,p=r.dots,m=r.indexes,y=(r.Blob||typeof Blob<"u"&&Blob)&&_.isSpecCompliantForm(u);if(!_.isFunction(s))throw new TypeError("visitor must be a function");function v(M){if(M===null)return"";if(_.isDate(M))return M.toISOString();if(_.isBoolean(M))return M.toString();if(!y&&_.isBlob(M))throw new it("Blob is not supported. Use a Buffer instead.");return _.isArrayBuffer(M)||_.isTypedArray(M)?y&&typeof Blob=="function"?new Blob([M]):Buffer.from(M):M}function x(M,L,U){let W=M;if(M&&!U&&typeof M=="object"){if(_.endsWith(L,"{}"))L=o?L:L.slice(0,-2),M=JSON.stringify(M);else if(_.isArray(M)&&I0(M)||(_.isFileList(M)||_.endsWith(L,"[]"))&&(W=_.toArray(M)))return L=Ov(L),W.forEach(function(Z,tt){!(_.isUndefined(Z)||Z===null)&&u.append(m===!0?Dm([L],tt,p):m===null?L:L+"[]",v(Z))}),!1}return Vr(M)?!0:(u.append(Dm(U,L,p),v(M)),!1)}const A=[],N=Object.assign(tb,{defaultVisitor:x,convertValue:v,isVisitable:Vr});function k(M,L){if(!_.isUndefined(M)){if(A.indexOf(M)!==-1)throw Error("Circular reference detected in "+L.join("."));A.push(M),_.forEach(M,function(W,J){(!(_.isUndefined(W)||W===null)&&s.call(u,W,_.isString(J)?J.trim():J,L,N))===!0&&k(W,L?L.concat(J):[J])}),A.pop()}}if(!_.isObject(l))throw new TypeError("data must be an object");return k(l),u}function wm(l){const u={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(o){return u[o]})}function ls(l,u){this._pairs=[],l&&nc(l,this,u)}const Rv=ls.prototype;Rv.append=function(u,r){this._pairs.push([u,r])};Rv.toString=function(u){const r=u?function(o){return u.call(this,o,wm)}:wm;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function eb(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function zv(l,u,r){if(!u)return l;const o=r&&r.encode||eb;_.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let p;if(s?p=s(u,r):p=_.isURLSearchParams(u)?u.toString():new ls(u,r).toString(o),p){const m=l.indexOf("#");m!==-1&&(l=l.slice(0,m)),l+=(l.indexOf("?")===-1?"?":"&")+p}return l}class jm{constructor(){this.handlers=[]}use(u,r,o){return this.handlers.push({fulfilled:u,rejected:r,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(u){this.handlers[u]&&(this.handlers[u]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(u){_.forEach(this.handlers,function(o){o!==null&&u(o)})}}const _v={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ab=typeof URLSearchParams<"u"?URLSearchParams:ls,nb=typeof FormData<"u"?FormData:null,lb=typeof Blob<"u"?Blob:null,ib={isBrowser:!0,classes:{URLSearchParams:ab,FormData:nb,Blob:lb},protocols:["http","https","file","blob","url","data"]},is=typeof window<"u"&&typeof document<"u",Zr=typeof navigator=="object"&&navigator||void 0,ub=is&&(!Zr||["ReactNative","NativeScript","NS"].indexOf(Zr.product)<0),cb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ob=is&&window.location.href||"http://localhost",rb=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:is,hasStandardBrowserEnv:ub,hasStandardBrowserWebWorkerEnv:cb,navigator:Zr,origin:ob},Symbol.toStringTag,{value:"Module"})),re={...rb,...ib};function sb(l,u){return nc(l,new re.classes.URLSearchParams,{visitor:function(r,o,s,p){return re.isNode&&_.isBuffer(r)?(this.append(o,r.toString("base64")),!1):p.defaultVisitor.apply(this,arguments)},...u})}function fb(l){return _.matchAll(/\w+|\[(\w*)]/g,l).map(u=>u[0]==="[]"?"":u[1]||u[0])}function pb(l){const u={},r=Object.keys(l);let o;const s=r.length;let p;for(o=0;o<s;o++)p=r[o],u[p]=l[p];return u}function Dv(l){function u(r,o,s,p){let m=r[p++];if(m==="__proto__")return!0;const g=Number.isFinite(+m),y=p>=r.length;return m=!m&&_.isArray(s)?s.length:m,y?(_.hasOwnProp(s,m)?s[m]=[s[m],o]:s[m]=o,!g):((!s[m]||!_.isObject(s[m]))&&(s[m]=[]),u(r,o,s[m],p)&&_.isArray(s[m])&&(s[m]=pb(s[m])),!g)}if(_.isFormData(l)&&_.isFunction(l.entries)){const r={};return _.forEachEntry(l,(o,s)=>{u(fb(o),s,r,0)}),r}return null}function db(l,u,r){if(_.isString(l))try{return(u||JSON.parse)(l),_.trim(l)}catch(o){if(o.name!=="SyntaxError")throw o}return(r||JSON.stringify)(l)}const Ai={transitional:_v,adapter:["xhr","http","fetch"],transformRequest:[function(u,r){const o=r.getContentType()||"",s=o.indexOf("application/json")>-1,p=_.isObject(u);if(p&&_.isHTMLForm(u)&&(u=new FormData(u)),_.isFormData(u))return s?JSON.stringify(Dv(u)):u;if(_.isArrayBuffer(u)||_.isBuffer(u)||_.isStream(u)||_.isFile(u)||_.isBlob(u)||_.isReadableStream(u))return u;if(_.isArrayBufferView(u))return u.buffer;if(_.isURLSearchParams(u))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),u.toString();let g;if(p){if(o.indexOf("application/x-www-form-urlencoded")>-1)return sb(u,this.formSerializer).toString();if((g=_.isFileList(u))||o.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return nc(g?{"files[]":u}:u,y&&new y,this.formSerializer)}}return p||s?(r.setContentType("application/json",!1),db(u)):u}],transformResponse:[function(u){const r=this.transitional||Ai.transitional,o=r&&r.forcedJSONParsing,s=this.responseType==="json";if(_.isResponse(u)||_.isReadableStream(u))return u;if(u&&_.isString(u)&&(o&&!this.responseType||s)){const m=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(u)}catch(g){if(m)throw g.name==="SyntaxError"?it.from(g,it.ERR_BAD_RESPONSE,this,null,this.response):g}}return u}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:re.classes.FormData,Blob:re.classes.Blob},validateStatus:function(u){return u>=200&&u<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};_.forEach(["delete","get","head","post","put","patch"],l=>{Ai.headers[l]={}});const mb=_.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),vb=l=>{const u={};let r,o,s;return l&&l.split(`
`).forEach(function(m){s=m.indexOf(":"),r=m.substring(0,s).trim().toLowerCase(),o=m.substring(s+1).trim(),!(!r||u[r]&&mb[r])&&(r==="set-cookie"?u[r]?u[r].push(o):u[r]=[o]:u[r]=u[r]?u[r]+", "+o:o)}),u},Um=Symbol("internals");function vi(l){return l&&String(l).trim().toLowerCase()}function Gu(l){return l===!1||l==null?l:_.isArray(l)?l.map(Gu):String(l)}function hb(l){const u=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=r.exec(l);)u[o[1]]=o[2];return u}const gb=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function jr(l,u,r,o,s){if(_.isFunction(o))return o.call(this,u,r);if(s&&(u=r),!!_.isString(u)){if(_.isString(o))return u.indexOf(o)!==-1;if(_.isRegExp(o))return o.test(u)}}function yb(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(u,r,o)=>r.toUpperCase()+o)}function xb(l,u){const r=_.toCamelCase(" "+u);["get","set","has"].forEach(o=>{Object.defineProperty(l,o+r,{value:function(s,p,m){return this[o].call(this,u,s,p,m)},configurable:!0})})}let ge=class{constructor(u){u&&this.set(u)}set(u,r,o){const s=this;function p(g,y,v){const x=vi(y);if(!x)throw new Error("header name must be a non-empty string");const A=_.findKey(s,x);(!A||s[A]===void 0||v===!0||v===void 0&&s[A]!==!1)&&(s[A||y]=Gu(g))}const m=(g,y)=>_.forEach(g,(v,x)=>p(v,x,y));if(_.isPlainObject(u)||u instanceof this.constructor)m(u,r);else if(_.isString(u)&&(u=u.trim())&&!gb(u))m(vb(u),r);else if(_.isObject(u)&&_.isIterable(u)){let g={},y,v;for(const x of u){if(!_.isArray(x))throw TypeError("Object iterator must return a key-value pair");g[v=x[0]]=(y=g[v])?_.isArray(y)?[...y,x[1]]:[y,x[1]]:x[1]}m(g,r)}else u!=null&&p(r,u,o);return this}get(u,r){if(u=vi(u),u){const o=_.findKey(this,u);if(o){const s=this[o];if(!r)return s;if(r===!0)return hb(s);if(_.isFunction(r))return r.call(this,s,o);if(_.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(u,r){if(u=vi(u),u){const o=_.findKey(this,u);return!!(o&&this[o]!==void 0&&(!r||jr(this,this[o],o,r)))}return!1}delete(u,r){const o=this;let s=!1;function p(m){if(m=vi(m),m){const g=_.findKey(o,m);g&&(!r||jr(o,o[g],g,r))&&(delete o[g],s=!0)}}return _.isArray(u)?u.forEach(p):p(u),s}clear(u){const r=Object.keys(this);let o=r.length,s=!1;for(;o--;){const p=r[o];(!u||jr(this,this[p],p,u,!0))&&(delete this[p],s=!0)}return s}normalize(u){const r=this,o={};return _.forEach(this,(s,p)=>{const m=_.findKey(o,p);if(m){r[m]=Gu(s),delete r[p];return}const g=u?yb(p):String(p).trim();g!==p&&delete r[p],r[g]=Gu(s),o[g]=!0}),this}concat(...u){return this.constructor.concat(this,...u)}toJSON(u){const r=Object.create(null);return _.forEach(this,(o,s)=>{o!=null&&o!==!1&&(r[s]=u&&_.isArray(o)?o.join(", "):o)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([u,r])=>u+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(u){return u instanceof this?u:new this(u)}static concat(u,...r){const o=new this(u);return r.forEach(s=>o.set(s)),o}static accessor(u){const o=(this[Um]=this[Um]={accessors:{}}).accessors,s=this.prototype;function p(m){const g=vi(m);o[g]||(xb(s,m),o[g]=!0)}return _.isArray(u)?u.forEach(p):p(u),this}};ge.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);_.reduceDescriptors(ge.prototype,({value:l},u)=>{let r=u[0].toUpperCase()+u.slice(1);return{get:()=>l,set(o){this[r]=o}}});_.freezeMethods(ge);function Ur(l,u){const r=this||Ai,o=u||r,s=ge.from(o.headers);let p=o.data;return _.forEach(l,function(g){p=g.call(r,p,s.normalize(),u?u.status:void 0)}),s.normalize(),p}function wv(l){return!!(l&&l.__CANCEL__)}function pl(l,u,r){it.call(this,l??"canceled",it.ERR_CANCELED,u,r),this.name="CanceledError"}_.inherits(pl,it,{__CANCEL__:!0});function jv(l,u,r){const o=r.config.validateStatus;!r.status||!o||o(r.status)?l(r):u(new it("Request failed with status code "+r.status,[it.ERR_BAD_REQUEST,it.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function bb(l){const u=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return u&&u[1]||""}function Sb(l,u){l=l||10;const r=new Array(l),o=new Array(l);let s=0,p=0,m;return u=u!==void 0?u:1e3,function(y){const v=Date.now(),x=o[p];m||(m=v),r[s]=y,o[s]=v;let A=p,N=0;for(;A!==s;)N+=r[A++],A=A%l;if(s=(s+1)%l,s===p&&(p=(p+1)%l),v-m<u)return;const k=x&&v-x;return k?Math.round(N*1e3/k):void 0}}function Eb(l,u){let r=0,o=1e3/u,s,p;const m=(v,x=Date.now())=>{r=x,s=null,p&&(clearTimeout(p),p=null),l(...v)};return[(...v)=>{const x=Date.now(),A=x-r;A>=o?m(v,x):(s=v,p||(p=setTimeout(()=>{p=null,m(s)},o-A)))},()=>s&&m(s)]}const Fu=(l,u,r=3)=>{let o=0;const s=Sb(50,250);return Eb(p=>{const m=p.loaded,g=p.lengthComputable?p.total:void 0,y=m-o,v=s(y),x=m<=g;o=m;const A={loaded:m,total:g,progress:g?m/g:void 0,bytes:y,rate:v||void 0,estimated:v&&g&&x?(g-m)/v:void 0,event:p,lengthComputable:g!=null,[u?"download":"upload"]:!0};l(A)},r)},Mm=(l,u)=>{const r=l!=null;return[o=>u[0]({lengthComputable:r,total:l,loaded:o}),u[1]]},Cm=l=>(...u)=>_.asap(()=>l(...u)),Tb=re.hasStandardBrowserEnv?((l,u)=>r=>(r=new URL(r,re.origin),l.protocol===r.protocol&&l.host===r.host&&(u||l.port===r.port)))(new URL(re.origin),re.navigator&&/(msie|trident)/i.test(re.navigator.userAgent)):()=>!0,Ab=re.hasStandardBrowserEnv?{write(l,u,r,o,s,p){const m=[l+"="+encodeURIComponent(u)];_.isNumber(r)&&m.push("expires="+new Date(r).toGMTString()),_.isString(o)&&m.push("path="+o),_.isString(s)&&m.push("domain="+s),p===!0&&m.push("secure"),document.cookie=m.join("; ")},read(l){const u=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return u?decodeURIComponent(u[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ob(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function Rb(l,u){return u?l.replace(/\/?\/$/,"")+"/"+u.replace(/^\/+/,""):l}function Uv(l,u,r){let o=!Ob(u);return l&&(o||r==!1)?Rb(l,u):u}const Nm=l=>l instanceof ge?{...l}:l;function vn(l,u){u=u||{};const r={};function o(v,x,A,N){return _.isPlainObject(v)&&_.isPlainObject(x)?_.merge.call({caseless:N},v,x):_.isPlainObject(x)?_.merge({},x):_.isArray(x)?x.slice():x}function s(v,x,A,N){if(_.isUndefined(x)){if(!_.isUndefined(v))return o(void 0,v,A,N)}else return o(v,x,A,N)}function p(v,x){if(!_.isUndefined(x))return o(void 0,x)}function m(v,x){if(_.isUndefined(x)){if(!_.isUndefined(v))return o(void 0,v)}else return o(void 0,x)}function g(v,x,A){if(A in u)return o(v,x);if(A in l)return o(void 0,v)}const y={url:p,method:p,data:p,baseURL:m,transformRequest:m,transformResponse:m,paramsSerializer:m,timeout:m,timeoutMessage:m,withCredentials:m,withXSRFToken:m,adapter:m,responseType:m,xsrfCookieName:m,xsrfHeaderName:m,onUploadProgress:m,onDownloadProgress:m,decompress:m,maxContentLength:m,maxBodyLength:m,beforeRedirect:m,transport:m,httpAgent:m,httpsAgent:m,cancelToken:m,socketPath:m,responseEncoding:m,validateStatus:g,headers:(v,x,A)=>s(Nm(v),Nm(x),A,!0)};return _.forEach(Object.keys({...l,...u}),function(x){const A=y[x]||s,N=A(l[x],u[x],x);_.isUndefined(N)&&A!==g||(r[x]=N)}),r}const Mv=l=>{const u=vn({},l);let{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:p,headers:m,auth:g}=u;u.headers=m=ge.from(m),u.url=zv(Uv(u.baseURL,u.url,u.allowAbsoluteUrls),l.params,l.paramsSerializer),g&&m.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let y;if(_.isFormData(r)){if(re.hasStandardBrowserEnv||re.hasStandardBrowserWebWorkerEnv)m.setContentType(void 0);else if((y=m.getContentType())!==!1){const[v,...x]=y?y.split(";").map(A=>A.trim()).filter(Boolean):[];m.setContentType([v||"multipart/form-data",...x].join("; "))}}if(re.hasStandardBrowserEnv&&(o&&_.isFunction(o)&&(o=o(u)),o||o!==!1&&Tb(u.url))){const v=s&&p&&Ab.read(p);v&&m.set(s,v)}return u},zb=typeof XMLHttpRequest<"u",_b=zb&&function(l){return new Promise(function(r,o){const s=Mv(l);let p=s.data;const m=ge.from(s.headers).normalize();let{responseType:g,onUploadProgress:y,onDownloadProgress:v}=s,x,A,N,k,M;function L(){k&&k(),M&&M(),s.cancelToken&&s.cancelToken.unsubscribe(x),s.signal&&s.signal.removeEventListener("abort",x)}let U=new XMLHttpRequest;U.open(s.method.toUpperCase(),s.url,!0),U.timeout=s.timeout;function W(){if(!U)return;const Z=ge.from("getAllResponseHeaders"in U&&U.getAllResponseHeaders()),Y={data:!g||g==="text"||g==="json"?U.responseText:U.response,status:U.status,statusText:U.statusText,headers:Z,config:l,request:U};jv(function(ot){r(ot),L()},function(ot){o(ot),L()},Y),U=null}"onloadend"in U?U.onloadend=W:U.onreadystatechange=function(){!U||U.readyState!==4||U.status===0&&!(U.responseURL&&U.responseURL.indexOf("file:")===0)||setTimeout(W)},U.onabort=function(){U&&(o(new it("Request aborted",it.ECONNABORTED,l,U)),U=null)},U.onerror=function(){o(new it("Network Error",it.ERR_NETWORK,l,U)),U=null},U.ontimeout=function(){let tt=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const Y=s.transitional||_v;s.timeoutErrorMessage&&(tt=s.timeoutErrorMessage),o(new it(tt,Y.clarifyTimeoutError?it.ETIMEDOUT:it.ECONNABORTED,l,U)),U=null},p===void 0&&m.setContentType(null),"setRequestHeader"in U&&_.forEach(m.toJSON(),function(tt,Y){U.setRequestHeader(Y,tt)}),_.isUndefined(s.withCredentials)||(U.withCredentials=!!s.withCredentials),g&&g!=="json"&&(U.responseType=s.responseType),v&&([N,M]=Fu(v,!0),U.addEventListener("progress",N)),y&&U.upload&&([A,k]=Fu(y),U.upload.addEventListener("progress",A),U.upload.addEventListener("loadend",k)),(s.cancelToken||s.signal)&&(x=Z=>{U&&(o(!Z||Z.type?new pl(null,l,U):Z),U.abort(),U=null)},s.cancelToken&&s.cancelToken.subscribe(x),s.signal&&(s.signal.aborted?x():s.signal.addEventListener("abort",x)));const J=bb(s.url);if(J&&re.protocols.indexOf(J)===-1){o(new it("Unsupported protocol "+J+":",it.ERR_BAD_REQUEST,l));return}U.send(p||null)})},Db=(l,u)=>{const{length:r}=l=l?l.filter(Boolean):[];if(u||r){let o=new AbortController,s;const p=function(v){if(!s){s=!0,g();const x=v instanceof Error?v:this.reason;o.abort(x instanceof it?x:new pl(x instanceof Error?x.message:x))}};let m=u&&setTimeout(()=>{m=null,p(new it(`timeout ${u} of ms exceeded`,it.ETIMEDOUT))},u);const g=()=>{l&&(m&&clearTimeout(m),m=null,l.forEach(v=>{v.unsubscribe?v.unsubscribe(p):v.removeEventListener("abort",p)}),l=null)};l.forEach(v=>v.addEventListener("abort",p));const{signal:y}=o;return y.unsubscribe=()=>_.asap(g),y}},wb=function*(l,u){let r=l.byteLength;if(r<u){yield l;return}let o=0,s;for(;o<r;)s=o+u,yield l.slice(o,s),o=s},jb=async function*(l,u){for await(const r of Ub(l))yield*wb(r,u)},Ub=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const u=l.getReader();try{for(;;){const{done:r,value:o}=await u.read();if(r)break;yield o}}finally{await u.cancel()}},qm=(l,u,r,o)=>{const s=jb(l,u);let p=0,m,g=y=>{m||(m=!0,o&&o(y))};return new ReadableStream({async pull(y){try{const{done:v,value:x}=await s.next();if(v){g(),y.close();return}let A=x.byteLength;if(r){let N=p+=A;r(N)}y.enqueue(new Uint8Array(x))}catch(v){throw g(v),v}},cancel(y){return g(y),s.return()}},{highWaterMark:2})},lc=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Cv=lc&&typeof ReadableStream=="function",Mb=lc&&(typeof TextEncoder=="function"?(l=>u=>l.encode(u))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),Nv=(l,...u)=>{try{return!!l(...u)}catch{return!1}},Cb=Cv&&Nv(()=>{let l=!1;const u=new Request(re.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!u}),Bm=64*1024,Kr=Cv&&Nv(()=>_.isReadableStream(new Response("").body)),$u={stream:Kr&&(l=>l.body)};lc&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(u=>{!$u[u]&&($u[u]=_.isFunction(l[u])?r=>r[u]():(r,o)=>{throw new it(`Response type '${u}' is not supported`,it.ERR_NOT_SUPPORT,o)})})})(new Response);const Nb=async l=>{if(l==null)return 0;if(_.isBlob(l))return l.size;if(_.isSpecCompliantForm(l))return(await new Request(re.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(_.isArrayBufferView(l)||_.isArrayBuffer(l))return l.byteLength;if(_.isURLSearchParams(l)&&(l=l+""),_.isString(l))return(await Mb(l)).byteLength},qb=async(l,u)=>{const r=_.toFiniteNumber(l.getContentLength());return r??Nb(u)},Bb=lc&&(async l=>{let{url:u,method:r,data:o,signal:s,cancelToken:p,timeout:m,onDownloadProgress:g,onUploadProgress:y,responseType:v,headers:x,withCredentials:A="same-origin",fetchOptions:N}=Mv(l);v=v?(v+"").toLowerCase():"text";let k=Db([s,p&&p.toAbortSignal()],m),M;const L=k&&k.unsubscribe&&(()=>{k.unsubscribe()});let U;try{if(y&&Cb&&r!=="get"&&r!=="head"&&(U=await qb(x,o))!==0){let Y=new Request(u,{method:"POST",body:o,duplex:"half"}),ft;if(_.isFormData(o)&&(ft=Y.headers.get("content-type"))&&x.setContentType(ft),Y.body){const[ot,Tt]=Mm(U,Fu(Cm(y)));o=qm(Y.body,Bm,ot,Tt)}}_.isString(A)||(A=A?"include":"omit");const W="credentials"in Request.prototype;M=new Request(u,{...N,signal:k,method:r.toUpperCase(),headers:x.normalize().toJSON(),body:o,duplex:"half",credentials:W?A:void 0});let J=await fetch(M,N);const Z=Kr&&(v==="stream"||v==="response");if(Kr&&(g||Z&&L)){const Y={};["status","statusText","headers"].forEach(xt=>{Y[xt]=J[xt]});const ft=_.toFiniteNumber(J.headers.get("content-length")),[ot,Tt]=g&&Mm(ft,Fu(Cm(g),!0))||[];J=new Response(qm(J.body,Bm,ot,()=>{Tt&&Tt(),L&&L()}),Y)}v=v||"text";let tt=await $u[_.findKey($u,v)||"text"](J,l);return!Z&&L&&L(),await new Promise((Y,ft)=>{jv(Y,ft,{data:tt,headers:ge.from(J.headers),status:J.status,statusText:J.statusText,config:l,request:M})})}catch(W){throw L&&L(),W&&W.name==="TypeError"&&/Load failed|fetch/i.test(W.message)?Object.assign(new it("Network Error",it.ERR_NETWORK,l,M),{cause:W.cause||W}):it.from(W,W&&W.code,l,M)}}),Jr={http:P0,xhr:_b,fetch:Bb};_.forEach(Jr,(l,u)=>{if(l){try{Object.defineProperty(l,"name",{value:u})}catch{}Object.defineProperty(l,"adapterName",{value:u})}});const Hm=l=>`- ${l}`,Hb=l=>_.isFunction(l)||l===null||l===!1,qv={getAdapter:l=>{l=_.isArray(l)?l:[l];const{length:u}=l;let r,o;const s={};for(let p=0;p<u;p++){r=l[p];let m;if(o=r,!Hb(r)&&(o=Jr[(m=String(r)).toLowerCase()],o===void 0))throw new it(`Unknown adapter '${m}'`);if(o)break;s[m||"#"+p]=o}if(!o){const p=Object.entries(s).map(([g,y])=>`adapter ${g} `+(y===!1?"is not supported by the environment":"is not available in the build"));let m=u?p.length>1?`since :
`+p.map(Hm).join(`
`):" "+Hm(p[0]):"as no adapter specified";throw new it("There is no suitable adapter to dispatch the request "+m,"ERR_NOT_SUPPORT")}return o},adapters:Jr};function Mr(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new pl(null,l)}function Lm(l){return Mr(l),l.headers=ge.from(l.headers),l.data=Ur.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),qv.getAdapter(l.adapter||Ai.adapter)(l).then(function(o){return Mr(l),o.data=Ur.call(l,l.transformResponse,o),o.headers=ge.from(o.headers),o},function(o){return wv(o)||(Mr(l),o&&o.response&&(o.response.data=Ur.call(l,l.transformResponse,o.response),o.response.headers=ge.from(o.response.headers))),Promise.reject(o)})}const Bv="1.11.0",ic={};["object","boolean","number","function","string","symbol"].forEach((l,u)=>{ic[l]=function(o){return typeof o===l||"a"+(u<1?"n ":" ")+l}});const km={};ic.transitional=function(u,r,o){function s(p,m){return"[Axios v"+Bv+"] Transitional option '"+p+"'"+m+(o?". "+o:"")}return(p,m,g)=>{if(u===!1)throw new it(s(m," has been removed"+(r?" in "+r:"")),it.ERR_DEPRECATED);return r&&!km[m]&&(km[m]=!0,console.warn(s(m," has been deprecated since v"+r+" and will be removed in the near future"))),u?u(p,m,g):!0}};ic.spelling=function(u){return(r,o)=>(console.warn(`${o} is likely a misspelling of ${u}`),!0)};function Lb(l,u,r){if(typeof l!="object")throw new it("options must be an object",it.ERR_BAD_OPTION_VALUE);const o=Object.keys(l);let s=o.length;for(;s-- >0;){const p=o[s],m=u[p];if(m){const g=l[p],y=g===void 0||m(g,p,l);if(y!==!0)throw new it("option "+p+" must be "+y,it.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new it("Unknown option "+p,it.ERR_BAD_OPTION)}}const Xu={assertOptions:Lb,validators:ic},Fe=Xu.validators;let mn=class{constructor(u){this.defaults=u||{},this.interceptors={request:new jm,response:new jm}}async request(u,r){try{return await this._request(u,r)}catch(o){if(o instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const p=s.stack?s.stack.replace(/^.+\n/,""):"";try{o.stack?p&&!String(o.stack).endsWith(p.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+p):o.stack=p}catch{}}throw o}}_request(u,r){typeof u=="string"?(r=r||{},r.url=u):r=u||{},r=vn(this.defaults,r);const{transitional:o,paramsSerializer:s,headers:p}=r;o!==void 0&&Xu.assertOptions(o,{silentJSONParsing:Fe.transitional(Fe.boolean),forcedJSONParsing:Fe.transitional(Fe.boolean),clarifyTimeoutError:Fe.transitional(Fe.boolean)},!1),s!=null&&(_.isFunction(s)?r.paramsSerializer={serialize:s}:Xu.assertOptions(s,{encode:Fe.function,serialize:Fe.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Xu.assertOptions(r,{baseUrl:Fe.spelling("baseURL"),withXsrfToken:Fe.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let m=p&&_.merge(p.common,p[r.method]);p&&_.forEach(["delete","get","head","post","put","patch","common"],M=>{delete p[M]}),r.headers=ge.concat(m,p);const g=[];let y=!0;this.interceptors.request.forEach(function(L){typeof L.runWhen=="function"&&L.runWhen(r)===!1||(y=y&&L.synchronous,g.unshift(L.fulfilled,L.rejected))});const v=[];this.interceptors.response.forEach(function(L){v.push(L.fulfilled,L.rejected)});let x,A=0,N;if(!y){const M=[Lm.bind(this),void 0];for(M.unshift(...g),M.push(...v),N=M.length,x=Promise.resolve(r);A<N;)x=x.then(M[A++],M[A++]);return x}N=g.length;let k=r;for(A=0;A<N;){const M=g[A++],L=g[A++];try{k=M(k)}catch(U){L.call(this,U);break}}try{x=Lm.call(this,k)}catch(M){return Promise.reject(M)}for(A=0,N=v.length;A<N;)x=x.then(v[A++],v[A++]);return x}getUri(u){u=vn(this.defaults,u);const r=Uv(u.baseURL,u.url,u.allowAbsoluteUrls);return zv(r,u.params,u.paramsSerializer)}};_.forEach(["delete","get","head","options"],function(u){mn.prototype[u]=function(r,o){return this.request(vn(o||{},{method:u,url:r,data:(o||{}).data}))}});_.forEach(["post","put","patch"],function(u){function r(o){return function(p,m,g){return this.request(vn(g||{},{method:u,headers:o?{"Content-Type":"multipart/form-data"}:{},url:p,data:m}))}}mn.prototype[u]=r(),mn.prototype[u+"Form"]=r(!0)});let kb=class Hv{constructor(u){if(typeof u!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(p){r=p});const o=this;this.promise.then(s=>{if(!o._listeners)return;let p=o._listeners.length;for(;p-- >0;)o._listeners[p](s);o._listeners=null}),this.promise.then=s=>{let p;const m=new Promise(g=>{o.subscribe(g),p=g}).then(s);return m.cancel=function(){o.unsubscribe(p)},m},u(function(p,m,g){o.reason||(o.reason=new pl(p,m,g),r(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(u){if(this.reason){u(this.reason);return}this._listeners?this._listeners.push(u):this._listeners=[u]}unsubscribe(u){if(!this._listeners)return;const r=this._listeners.indexOf(u);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const u=new AbortController,r=o=>{u.abort(o)};return this.subscribe(r),u.signal.unsubscribe=()=>this.unsubscribe(r),u.signal}static source(){let u;return{token:new Hv(function(s){u=s}),cancel:u}}};function Yb(l){return function(r){return l.apply(null,r)}}function Gb(l){return _.isObject(l)&&l.isAxiosError===!0}const Fr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fr).forEach(([l,u])=>{Fr[u]=l});function Lv(l){const u=new mn(l),r=vv(mn.prototype.request,u);return _.extend(r,mn.prototype,u,{allOwnKeys:!0}),_.extend(r,u,null,{allOwnKeys:!0}),r.create=function(s){return Lv(vn(l,s))},r}const Vt=Lv(Ai);Vt.Axios=mn;Vt.CanceledError=pl;Vt.CancelToken=kb;Vt.isCancel=wv;Vt.VERSION=Bv;Vt.toFormData=nc;Vt.AxiosError=it;Vt.Cancel=Vt.CanceledError;Vt.all=function(u){return Promise.all(u)};Vt.spread=Yb;Vt.isAxiosError=Gb;Vt.mergeConfig=vn;Vt.AxiosHeaders=ge;Vt.formToJSON=l=>Dv(_.isHTMLForm(l)?new FormData(l):l);Vt.getAdapter=qv.getAdapter;Vt.HttpStatusCode=Fr;Vt.default=Vt;const{Axios:l1,AxiosError:i1,CanceledError:u1,isCancel:c1,CancelToken:o1,VERSION:r1,all:s1,Cancel:f1,isAxiosError:p1,spread:d1,toFormData:m1,AxiosHeaders:v1,HttpStatusCode:h1,formToJSON:g1,getAdapter:y1,mergeConfig:x1}=Vt,ke=Vt.create({baseURL:"http://localhost:8000/api",withCredentials:!1});ke.interceptors.response.use(l=>l,l=>l?.response?.data?Promise.reject(l):Promise.reject({response:{data:{detail:"Network error"}}}));async function Xb(l){return(await ke.post("/materials/topic",l)).data}async function Qb(l){return(await ke.post("/materials/text",l)).data}async function Vb(l,u){return(await ke.post("/materials/upload",l,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:u})).data}const Zb=({userAge:l,hasParentalConsent:u=!1,onMaterialCreated:r,onError:o})=>{const[s,p]=D.useState("topic"),[m,g]=D.useState(""),[y,v]=D.useState(""),[x,A]=D.useState(""),[N,k]=D.useState({isUploading:!1,progress:0,message:"",type:"info"}),M=l>=13||u,L=async()=>{if(!m.trim()){o?.("Please enter a topic to study");return}k({isUploading:!0,progress:50,message:"Creating study material...",type:"info"});try{const Y=await Xb({topic:m.trim(),age:l});k({isUploading:!1,progress:100,message:"Study material created successfully!",type:"success"}),r?.(Y),g("")}catch(Y){const ot=Y?.response?.data?.detail||"Failed to create study material";k({isUploading:!1,progress:0,message:ot,type:"error"}),o?.(ot)}},U=async()=>{if(!y.trim()){o?.("Please paste some text content");return}if(y.length<10){o?.("Text content is too short. Please provide at least 10 characters.");return}k({isUploading:!0,progress:50,message:"Processing text content...",type:"info"});try{const Y=await Qb({content:y.trim(),title:x.trim()||void 0,age:l});k({isUploading:!1,progress:100,message:"Text processed successfully!",type:"success"}),r?.(Y),v(""),A("")}catch(Y){const ot=Y?.response?.data?.detail||"Failed to process text";k({isUploading:!1,progress:0,message:ot,type:"error"}),o?.(ot)}},W=async Y=>{if(Y.length===0)return;const ft=Y[0];if(!["application/pdf","text/plain"].includes(ft.type)){o?.("Only PDF and TXT files are supported");return}const Tt=ft.type==="application/pdf"?10*1024*1024:5*1024*1024;if(ft.size>Tt){const xt=ft.type==="application/pdf"?"10MB":"5MB";o?.(`File size exceeds ${xt} limit`);return}k({isUploading:!0,progress:0,message:"Uploading file...",type:"info"});try{const xt=new FormData;xt.append("file",ft),xt.append("user_age",l.toString()),xt.append("has_parental_consent",u.toString()),await Vb(xt,Yt=>{if(Yt.total){const Gt=Math.round(Yt.loaded*100/Yt.total);k(Bt=>({...Bt,progress:Gt,message:Gt<100?`Uploading file... ${Gt}%`:"Processing file..."}))}})}catch(xt){const Gt=xt?.response?.data?.detail||"File upload failed";k({isUploading:!1,progress:0,message:Gt,type:"error"}),o?.(Gt)}},{getRootProps:J,getInputProps:Z,isDragActive:tt}=mv({onDrop:W,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"]},maxFiles:1,disabled:!M||N.isUploading});return B.jsxs("div",{className:"material-input-container",style:{maxWidth:"600px",margin:"0 auto",padding:"20px"},children:[B.jsx("h2",{style:{textAlign:"center",marginBottom:"20px"},children:"📚 Add Study Material"}),B.jsxs("div",{className:"tabs",style:{marginBottom:"20px"},children:[B.jsx("button",{onClick:()=>p("topic"),className:"tab","aria-selected":s==="topic",children:"📝 Enter Topic"}),B.jsx("button",{onClick:()=>p("text"),className:"tab","aria-selected":s==="text",children:"📋 Paste Text"}),B.jsx("button",{onClick:()=>p("file"),disabled:!M,className:"tab","aria-selected":s==="file",style:{opacity:M?1:.5,cursor:M?"pointer":"not-allowed"},children:"📁 Upload File"})]}),s==="topic"&&B.jsxs("div",{className:"topic-tab",children:[B.jsx("p",{style:{marginBottom:"15px",color:"#666"},children:"Enter a topic you'd like to study (perfect for ages 7-12):"}),B.jsx("input",{type:"text",value:m,onChange:Y=>g(Y.target.value),placeholder:"e.g., Photosynthesis, Ancient Egypt, Solar System...",disabled:N.isUploading,className:"input",style:{marginBottom:"15px"},maxLength:200}),B.jsx("button",{onClick:L,disabled:!m.trim()||N.isUploading,className:"btn btn-primary",style:{width:"100%"},children:N.isUploading?"Creating...":"🚀 Start Studying"})]}),s==="text"&&B.jsxs("div",{className:"text-tab",children:[B.jsx("p",{style:{marginBottom:"15px",color:"#666"},children:"Copy and paste text from websites, textbooks, or other sources:"}),B.jsx("input",{type:"text",value:x,onChange:Y=>A(Y.target.value),placeholder:"Optional: Give your material a title",disabled:N.isUploading,className:"input",style:{marginBottom:"10px"},maxLength:255}),B.jsx("textarea",{value:y,onChange:Y=>v(Y.target.value),placeholder:"Paste your text content here...",disabled:N.isUploading,rows:8,className:"textarea",style:{marginBottom:"15px",fontFamily:"monospace"},maxLength:1e4}),B.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[B.jsxs("span",{style:{fontSize:"12px",color:"#666"},children:[y.length,"/10,000 characters"]}),y.length>0&&B.jsxs("span",{style:{fontSize:"12px",color:"#666"},children:["~",Math.ceil(y.split(" ").length/150)," chunks will be created"]})]}),B.jsx("button",{onClick:U,disabled:y.length<10||N.isUploading,className:"btn btn-success",style:{width:"100%"},children:N.isUploading?"Processing...":"📄 Process Text"})]}),s==="file"&&B.jsx("div",{className:"file-tab",children:M?B.jsxs(B.Fragment,{children:[B.jsx("p",{style:{marginBottom:"15px",color:"#666"},children:"Upload PDF or text files (PDF max 10MB, TXT max 5MB):"}),B.jsxs("div",{...J(),style:{border:`2px dashed ${tt?"var(--color-accent)":"var(--color-border)"}`,borderRadius:"var(--radius-md)",padding:"40px",textAlign:"center",background:tt?"var(--color-accent-soft)":"#fafafa",cursor:N.isUploading?"not-allowed":"pointer",opacity:N.isUploading?.6:1,marginBottom:"15px"},children:[B.jsx("input",{...Z()}),B.jsx("div",{style:{fontSize:"48px",marginBottom:"10px"},children:tt?"🎯":"📁"}),B.jsx("p",{style:{margin:"0 0 5px 0",fontSize:"16px",color:"#333"},children:tt?"Drop your file here!":"Drag & drop a file here, or click to browse"}),B.jsx("p",{style:{margin:0,fontSize:"14px",color:"#666"},children:"Supports PDF and TXT files"})]})]}):B.jsxs("div",{style:{textAlign:"center",padding:"40px",backgroundColor:"#fff3cd",borderRadius:"8px",border:"1px solid #ffeaa7"},children:[B.jsx("p",{style:{margin:"0 0 10px 0",color:"#856404"},children:"🔒 File uploads are available for users 13+ or with parental consent."}),B.jsx("p",{style:{margin:0,fontSize:"14px",color:"#856404"},children:l<13?"Ask your parent to enable file uploads in parental controls.":"You can use the other options above!"})]})}),N.message&&B.jsxs("div",{className:`pill ${N.type==="success"?"status-success":N.type==="error"?"status-danger":"status-info"}`,style:{display:"block",textAlign:"center",marginTop:"20px",padding:"16px"},children:[N.isUploading&&B.jsx("div",{style:{marginBottom:"10px"},children:B.jsx("div",{style:{width:"100%",height:"8px",backgroundColor:"var(--color-border)",borderRadius:"6px",overflow:"hidden"},children:B.jsx("div",{style:{width:`${N.progress}%`,height:"100%",backgroundColor:"var(--color-accent)",transition:"width 0.3s ease"}})})}),B.jsx("p",{style:{margin:0},children:N.message})]}),l<13&&B.jsxs("div",{style:{marginTop:"20px",padding:"15px",background:"var(--color-info-soft)",border:"1px solid #bae6fd",borderRadius:"var(--radius-sm)",fontSize:"14px",color:"#0c4a6e"},children:[B.jsxs("p",{style:{margin:"0 0 5px 0"},children:["🛡️ ",B.jsx("strong",{children:"Safe Learning Environment"})]}),B.jsx("p",{style:{margin:0},children:"Your account has special protections for young learners. File uploads require parental approval."})]})]})};let kv=null;function hi(){return kv}function $r(l){kv=l}function Cr(){try{return localStorage.getItem("refresh_token")}catch{return null}}function Wr(l){try{l?localStorage.setItem("refresh_token",l):localStorage.removeItem("refresh_token")}catch{}}function Ym(){$r(null),Wr(null)}const Yv=D.createContext(void 0);let Gm=!1;function Kb(l){Gm||(Gm=!0,ke.interceptors.request.use(u=>{const r=hi();return r&&(u.headers=u.headers??{},u.headers.Authorization=`Bearer ${r}`),u}),ke.interceptors.response.use(u=>u,async u=>{const r=u.config;if(u?.response?.status===401&&!r?._retry&&(r._retry=!0,await l())){const s=hi();return s&&(r.headers=r.headers??{},r.headers.Authorization=`Bearer ${s}`),ke.request(r)}return Promise.reject(u)}))}function Jb({children:l}){const[u,r]=D.useState(null),[o,s]=D.useState(hi()),[p,m]=D.useState(!0),g=D.useCallback(async()=>{const k=Cr();if(!k)return!1;try{const{data:M}=await ke.post("/auth/refresh",{refresh_token:k});return $r(M.access_token),Wr(M.refresh_token),s(M.access_token),!0}catch{return Ym(),r(null),s(null),!1}},[]);D.useEffect(()=>{Kb(g)},[g]);const y=D.useCallback(async()=>{try{m(!0);const{data:k}=await ke.get("/auth/me");r(k)}catch{r(null)}finally{m(!1)}},[]);D.useEffect(()=>{(async()=>(!!Cr()&&!hi()&&await g(),hi()?await y():m(!1)))()},[y,g]);const v=D.useCallback(async k=>{const{data:M}=await ke.post("/auth/login",k);$r(M.access_token),Wr(M.refresh_token),s(M.access_token),r(M.user)},[]),x=D.useCallback(async()=>{const k=Cr();try{k&&await ke.post("/auth/logout",{refresh_token:k})}catch{}finally{Ym(),r(null),s(null)}},[]),A=D.useMemo(()=>u?u.age>=13:!1,[u]),N=D.useMemo(()=>({user:u,isLoading:p,isAuthenticated:!!u,accessToken:o,hasParentalConsent:A,login:v,logout:x,refresh:g}),[u,p,o,A,v,x,g]);return B.jsx(Yv.Provider,{value:N,children:l})}function Fb(){const l=D.useContext(Yv);if(!l)throw new Error("useAuth must be used within AuthProvider");return l}function $b(){const{user:l,isLoading:u}=Fb(),[r,o]=D.useState([]),[s,p]=D.useState(""),m=v=>{o(x=>[...x,v]),p(`✅ Successfully created: ${v.title||"New material"}`),setTimeout(()=>p(""),5e3)},g=v=>{p(`❌ Error: ${v}`),setTimeout(()=>p(""),5e3)},y=D.useMemo(()=>l?.age??0,[l]);return u?B.jsx(ul,{children:B.jsx("section",{children:B.jsxs("header",{style:{textAlign:"center",marginBottom:16},children:[B.jsx("h1",{children:"Materials"}),B.jsx("p",{children:"Loading session..."})]})})}):B.jsx(ul,{children:B.jsxs("section",{children:[B.jsxs("header",{style:{textAlign:"center",marginBottom:24},children:[B.jsx("h1",{children:"Materials"}),B.jsx("div",{className:"pill status-info",style:{marginTop:10},children:l?B.jsxs(B.Fragment,{children:["👤 ",l.first_name," ",l.last_name," · Age ",y,y<13&&" (COPPA Protected)"]}):B.jsx(B.Fragment,{children:"Not signed in"})})]}),B.jsx(Zb,{userAge:y,hasParentalConsent:!0,onMaterialCreated:m,onError:g}),s&&B.jsx("div",{className:`pill ${s.startsWith("✅")?"status-success":"status-danger"}`,style:{display:"block",textAlign:"center",marginTop:16},children:s}),r.length>0&&B.jsxs("div",{style:{marginTop:32},children:[B.jsxs("h3",{style:{marginBottom:16},children:["📚 Your Study Materials (",r.length,")"]}),B.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(260px, 1fr))",gap:14},children:r.map((v,x)=>B.jsxs("div",{className:"card",style:{padding:16},children:[B.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[B.jsxs("div",{children:[B.jsx("strong",{children:v.title}),v.chunk_count&&B.jsxs("span",{style:{marginLeft:10,color:"var(--color-muted)",fontSize:14},children:["(",v.chunk_count," chunks)"]})]}),B.jsx("span",{className:"pill status-success",children:"Ready"})]}),B.jsx("p",{style:{margin:"5px 0 0 0",fontSize:14,color:"var(--color-muted)"},children:v.message})]},x))})]})]})})}function Wb(){return B.jsxs(ul,{children:[B.jsx("h1",{children:"Study Buddy (AI Chat)"}),B.jsx("p",{children:"Coming soon: safe, multi-layer moderated chat per PRD 5.3."})]})}function Pb(){return B.jsxs(ul,{children:[B.jsx("h1",{children:"Quiz"}),B.jsx("p",{children:"Coming soon: AI-generated quizzes with points and badges per PRD 5.4."})]})}function Ib(){return B.jsxs(ul,{children:[B.jsx("h1",{children:"Progress Dashboard"}),B.jsx("p",{children:"Coming soon: student and parent views per PRD 5.5."})]})}function t1(){return B.jsxs(ul,{children:[B.jsx("h1",{children:"Parental Controls"}),B.jsx("p",{children:"Coming soon: consent flow and permissions per PRD 5.1."})]})}function e1(){return B.jsx(Jb,{children:B.jsx(cx,{children:B.jsxs(Iy,{children:[B.jsx(Ga,{path:"/",element:B.jsx(sm,{to:"/materials",replace:!0})}),B.jsx(Ga,{path:"/materials",element:B.jsx($b,{})}),B.jsx(Ga,{path:"/chat",element:B.jsx(Wb,{})}),B.jsx(Ga,{path:"/quiz",element:B.jsx(Pb,{})}),B.jsx(Ga,{path:"/dashboard",element:B.jsx(Ib,{})}),B.jsx(Ga,{path:"/parental-controls",element:B.jsx(t1,{})}),B.jsx(Ga,{path:"*",element:B.jsx(sm,{to:"/materials",replace:!0})})]})})})}sy.createRoot(document.getElementById("root")).render(B.jsx(D.StrictMode,{children:B.jsx(e1,{})}));
