{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/app/layouts/mainlayout.tsx", "../../src/app/ui/navbar.tsx", "../../src/components/materialinput.test.tsx", "../../src/components/materialinput.tsx", "../../src/features/chat/pages/chatpage.tsx", "../../src/features/coppa/pages/parentalcontrolspage.tsx", "../../src/features/dashboard/pages/dashboardpage.tsx", "../../src/features/materials/components/materialinput.tsx", "../../src/features/materials/pages/materialspage.test.tsx", "../../src/features/materials/pages/materialspage.tsx", "../../src/features/quiz/pages/quizpage.tsx", "../../src/shared/auth/authcontext.tsx", "../../src/shared/auth/tokenstorage.ts", "../../src/shared/lib/api.ts", "../../src/shared/lib/materialsapi.ts", "../../src/shared/types/index.ts", "../../src/shared/types/openapi.ts", "../../src/tests/setup.ts", "../../src/types/global.d.ts"], "version": "5.8.3"}