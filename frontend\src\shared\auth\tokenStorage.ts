export type AuthTokens = {
	accessToken: string | null;
	refreshToken: string | null;
};

let inMemoryAccessToken: string | null = null;

export function getAccessToken(): string | null {
	return inMemoryAccessToken;
}

export function setAccessToken(token: string | null): void {
	inMemoryAccessToken = token;
}

// We support both localStorage (remember me) and sessionStorage (session only)
type RefreshStorage = 'local' | 'session';

const REFRESH_TOKEN_KEY = 'refresh_token';
const REFRESH_STORAGE_KEY = 'refresh_token_persistence';

export function getRefreshToken(): string | null {
  try {
    // Prefer session storage when present
    const sessionVal = sessionStorage.getItem(REFRESH_TOKEN_KEY);
    if (sessionVal) return sessionVal;
  } catch {
    // ignore
  }
  try {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  } catch {
    return null;
  }
}

export function getRefreshTokenStorage(): RefreshStorage | null {
  try {
    const v = localStorage.getItem(REFRESH_STORAGE_KEY);
    return (v as RefreshStorage) || null;
  } catch {
    return null;
  }
}

export function setRefreshToken(token: string | null, remember: boolean = true): void {
  const target: RefreshStorage = remember ? 'local' : 'session';
  try {
    if (target === 'local') {
      // Clear session storage copy
      try { sessionStorage.removeItem(REFRESH_TOKEN_KEY); } catch {}
      if (token) {
        localStorage.setItem(REFRESH_TOKEN_KEY, token);
        localStorage.setItem(REFRESH_STORAGE_KEY, 'local');
      } else {
        localStorage.removeItem(REFRESH_TOKEN_KEY);
        localStorage.removeItem(REFRESH_STORAGE_KEY);
      }
    } else {
      // Session only
      try {
        if (token) {
          sessionStorage.setItem(REFRESH_TOKEN_KEY, token);
        } else {
          sessionStorage.removeItem(REFRESH_TOKEN_KEY);
        }
      } catch {}
      // Also clean any persisted local copy/flag
      try {
        localStorage.removeItem(REFRESH_TOKEN_KEY);
        localStorage.removeItem(REFRESH_STORAGE_KEY);
      } catch {}
    }
  } catch {
    // ignore storage errors (private mode, etc.)
  }
}

export function clearTokens(): void {
	setAccessToken(null);
  setRefreshToken(null, true);
}
