import {FormEvent, useMemo, useState} from 'react';
import {useNavigate, useLocation, Link} from 'react-router-dom';
import {useAuth} from '@shared/auth/AuthContext';

export default function LoginPage() {
  const navigate = useNavigate();
  const location = useLocation() as unknown as {state?: {from?: string}};
  const {login, isAuthenticated} = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [twoFactorToken, setTwoFactorToken] = useState('');
  const [remember, setRemember] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const from = useMemo(() => location?.state?.from || '/materials', [location]);

  async function onSubmit(e: FormEvent) {
    e.preventDefault();
    setError(null);
    try {
      await login({
        email,
        password,
        two_factor_token: twoFactorToken || undefined,
        remember_me: remember,
      });
      navigate(from, {replace: true});
    } catch (err: any) {
      const detail = err?.response?.data?.detail || 'Login failed';
      setError(Array.isArray(detail) ? detail[0]?.msg ?? 'Login failed' : detail);
    }
  }

  if (isAuthenticated) {
    navigate(from, {replace: true});
    return null;
  }

  return (
    <div className="container" style={{maxWidth: 420, marginTop: 32}}>
      <h2>Sign in</h2>
      {error && (
        <div className="alert alert-error" role="alert" style={{marginBottom: 12}}>
          {error}
        </div>
      )}
      <form onSubmit={onSubmit} className="card" style={{padding: 16}}>
        <label className="form-label">
          Email
          <input
            className="form-input"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </label>
        <label className="form-label">
          Password
          <input
            className="form-input"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </label>
        <label className="form-label">
          2FA Token (if enabled)
          <input
            className="form-input"
            type="text"
            inputMode="numeric"
            value={twoFactorToken}
            onChange={(e) => setTwoFactorToken(e.target.value)}
            placeholder="123456"
          />
        </label>
        <label className="form-checkbox" style={{display: 'flex', gap: 8, alignItems: 'center'}}>
          <input type="checkbox" checked={remember} onChange={(e) => setRemember(e.target.checked)} />
          Remember me
        </label>
        <button className="btn btn-primary" type="submit" style={{marginTop: 12}}>
          Sign in
        </button>
      </form>
      <p style={{marginTop: 12}}>
        New here? <Link to="/register">Create an account</Link>
      </p>
    </div>
  );
}


