import React, {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useState,
} from 'react';
import {apiClient} from '../lib/api';
import {
	clearTokens,
	getAccessToken,
	getRefreshToken,
	setAccessToken,
	setRefreshToken,
} from './tokenStorage';

type Role = 'admin' | 'parent' | 'child';

export type AuthUser = {
	id: number;
	email: string;
	first_name: string;
	last_name: string;
	role: Role;
	age: number;
	is_minor: boolean;
};

type LoginPayload = {
	email: string;
	password: string;
	two_factor_token?: string;
	remember_me?: boolean;
};

type AuthContextValue = {
	user: AuthUser | null;
	isLoading: boolean;
	isAuthenticated: boolean;
	accessToken: string | null;
	hasParentalConsent: boolean; // derived from is_minor + backend consent rules via features
	login: (payload: LoginPayload) => Promise<void>;
  register: (payload: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    date_of_birth: string;
    username?: string;
  }) => Promise<void>;
	logout: () => Promise<void>;
	refresh: () => Promise<boolean>;
};

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

let isInterceptorAttached = false;
let refreshInFlight: Promise<boolean> | null = null;

function attachInterceptors(refreshFn: () => Promise<boolean>) {
	if (isInterceptorAttached) return;
	isInterceptorAttached = true;

	apiClient.interceptors.request.use((config) => {
		const token = getAccessToken();
		if (token) {
			config.headers = config.headers ?? {};
			(config.headers as Record<string, string>)[
				'Authorization'
			] = `Bearer ${token}`;
		}
		return config;
	});

  apiClient.interceptors.response.use(
		(res) => res,
		async (error) => {
			const original = error.config;
      // Avoid refresh loop for refresh endpoint itself
      const isRefreshRequest = typeof original?.url === 'string' && original.url.includes('/auth/auth/refresh');
      if (error?.response?.status === 401 && !original?._retry && !isRefreshRequest) {
				original._retry = true;
        // Single-flight refresh to dedupe concurrent 401s
        if (!refreshInFlight) {
          refreshInFlight = refreshFn().finally(() => {
            refreshInFlight = null;
          });
        }
        const ok = await refreshInFlight;
				if (ok) {
					const token = getAccessToken();
					if (token) {
						original.headers = original.headers ?? {};
						original.headers['Authorization'] = `Bearer ${token}`;
					}
					return apiClient.request(original);
				}
			}
			return Promise.reject(error);
		}
	);
}

export function AuthProvider({children}: {children: React.ReactNode}) {
	const [user, setUser] = useState<AuthUser | null>(null);
	const [accessToken, setAccess] = useState<string | null>(getAccessToken());
	const [isLoading, setIsLoading] = useState<boolean>(true);

  const refresh = useCallback(async (): Promise<boolean> => {
		const refreshToken = getRefreshToken();
		if (!refreshToken) return false;
		try {
      const {data} = await apiClient.post('/auth/auth/refresh', {
				refresh_token: refreshToken,
			});
			setAccessToken(data.access_token);
			setRefreshToken(data.refresh_token);
			setAccess(data.access_token);
			return true;
		} catch {
			clearTokens();
			setUser(null);
			setAccess(null);
			return false;
		}
	}, []);

	// Attach interceptors once
	useEffect(() => {
		attachInterceptors(refresh);
	}, [refresh]);

  const hydrate = useCallback(async () => {
		try {
			setIsLoading(true);
      const {data} = await apiClient.get<AuthUser>('/auth/auth/me');
			setUser(data);
		} catch {
			setUser(null);
		} finally {
			setIsLoading(false);
		}
	}, []);

	// On mount, if we have refresh token try refresh then hydrate
	useEffect(() => {
		(async () => {
			const hasRefresh = !!getRefreshToken();
			if (hasRefresh && !getAccessToken()) {
				await refresh();
			}
			if (getAccessToken()) {
				await hydrate();
			} else {
				setIsLoading(false);
			}
		})();
	}, [hydrate, refresh]);

  const login = useCallback(async (payload: LoginPayload) => {
    const {data} = await apiClient.post('/auth/auth/login', payload);
		setAccessToken(data.access_token);
    setRefreshToken(data.refresh_token, payload.remember_me ?? true);
		setAccess(data.access_token);
		setUser(data.user);
	}, []);

  const register = useCallback(
    async (payload: {
      email: string;
      password: string;
      first_name: string;
      last_name: string;
      date_of_birth: string; // ISO string
      username?: string;
    }) => {
      const {data} = await apiClient.post('/auth/auth/register', payload);
      setAccessToken(data.access_token);
      // Default to remember on register for a smoother first session
      setRefreshToken(data.refresh_token, true);
      setAccess(data.access_token);
      setUser(data.user);
    },
    []
  );

	const logout = useCallback(async () => {
		const rt = getRefreshToken();
		try {
			if (rt) {
        await apiClient.post('/auth/auth/logout', {refresh_token: rt});
			}
		} catch {
			// ignore
		} finally {
			clearTokens();
			setUser(null);
			setAccess(null);
		}
	}, []);

	const hasParentalConsent = useMemo(() => {
		if (!user) return false;
		// We treat >= 13 as not requiring consent.
		// For minors, the backend enforces consent on certain endpoints. Frontend feature gating follows age.
		return user.age >= 13;
	}, [user]);

  const value = useMemo<AuthContextValue>(
		() => ({
			user,
			isLoading,
			isAuthenticated: !!user,
			accessToken,
			hasParentalConsent,
			login,
      register,
			logout,
			refresh,
		}),
    [user, isLoading, accessToken, hasParentalConsent, login, register, logout, refresh]
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// eslint-disable-next-line react-refresh/only-export-components
export function useAuth(): AuthContextValue {
	const ctx = useContext(AuthContext);
	if (!ctx) throw new Error('useAuth must be used within AuthProvider');
	return ctx;
}
