import {useMemo, useState} from 'react';
import {MainLayout} from '@app/layouts/MainLayout';
import MaterialInput from '../components/MaterialInput';
import type {Material} from '@shared/types';
import {useAuth} from '@shared/auth/AuthContext';

export default function MaterialsPage() {
    const {user, isLoading, isAuthenticated} = useAuth();
	const [materials, setMaterials] = useState<Material[]>([]);
	const [message, setMessage] = useState<string>('');

	const handleMaterialCreated = (result: Material) => {
		setMaterials((prev: Material[]) => [...prev, result]);
		setMessage(`✅ Successfully created: ${result.title || 'New material'}`);
		setTimeout(() => setMessage(''), 5000);
	};

	const handleError = (error: string) => {
		setMessage(`❌ Error: ${error}`);
		setTimeout(() => setMessage(''), 5000);
	};

	const userAge = useMemo(() => user?.age ?? 0, [user]);

	if (isLoading) {
		return (
			<MainLayout>
				<section>
					<header style={{textAlign: 'center', marginBottom: 16}}>
						<h1>Materials</h1>
						<p>Loading session...</p>
					</header>
				</section>
			</MainLayout>
		);
	}

    return (
		<MainLayout>
			<section>
				<header style={{textAlign: 'center', marginBottom: 24}}>
					<h1>Materials</h1>
					<div className='pill status-info' style={{marginTop: 10}}>
						{user ? (
							<>
								👤 {user.first_name} {user.last_name} · Age {userAge}
								{userAge < 13 && ' (COPPA Protected)'}
							</>
						) : (
							<>Not signed in</>
						)}
					</div>
				</header>
                {!isAuthenticated ? (
                    <div
                        className='pill status-danger'
                        style={{display: 'block', textAlign: 'center'}}>
                        Please sign in to add or upload study materials.
                    </div>
                ) : (
                    <MaterialInput
                        userAge={userAge}
                        hasParentalConsent={true}
                        onMaterialCreated={handleMaterialCreated}
                        onError={handleError}
                    />
                )}
				{message && (
					<div
						className={`pill ${
							message.startsWith('✅') ? 'status-success' : 'status-danger'
						}`}
						style={{display: 'block', textAlign: 'center', marginTop: 16}}>
						{message}
					</div>
				)}
				{materials.length > 0 && (
					<div style={{marginTop: 32}}>
						<h3 style={{marginBottom: 16}}>
							📚 Your Study Materials ({materials.length})
						</h3>
						<div
							style={{
								display: 'grid',
								gridTemplateColumns: 'repeat(auto-fill, minmax(260px, 1fr))',
								gap: 14,
							}}>
							{materials.map((material: Material, index: number) => (
								<div key={index} className='card' style={{padding: 16}}>
									<div
										style={{
											display: 'flex',
											justifyContent: 'space-between',
											alignItems: 'center',
										}}>
										<div>
											<strong>{material.title}</strong>
											{material.chunk_count && (
												<span
													style={{
														marginLeft: 10,
														color: 'var(--color-muted)',
														fontSize: 14,
													}}>
													({material.chunk_count} chunks)
												</span>
											)}
										</div>
										<span className='pill status-success'>Ready</span>
									</div>
									<p
										style={{
											margin: '5px 0 0 0',
											fontSize: 14,
											color: 'var(--color-muted)',
										}}>
										{material.message}
									</p>
								</div>
							))}
						</div>
					</div>
				)}
			</section>
		</MainLayout>
	);
}
