import {PropsWithChildren} from 'react';
import {Navigate, useLocation} from 'react-router-dom';
import {useAuth} from './AuthContext';

export function RequireAuth({children}: PropsWithChildren) {
  const {isAuthenticated, isLoading} = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <div style={{padding: 24}}>Loading...</div>;
  }

  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
        state={{from: location.pathname + location.search}}
      />
    );
  }

  return <>{children}</>;
}

export function RequireRole({
  children,
  roles,
}: PropsWithChildren<{roles: Array<'admin' | 'parent' | 'child'>}>) {
  const {isAuthenticated, isLoading, user} = useAuth();
  const location = useLocation();

  if (isLoading) return <div style={{padding: 24}}>Loading...</div>;
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
        state={{from: location.pathname + location.search}}
      />
    );
  }
  if (!user || !roles.includes(user.role)) {
    return <Navigate to="/" replace />;
  }
  return <>{children}</>;
}


