import {Link, NavLink} from 'react-router-dom';
import {useAuth} from '@shared/auth/AuthContext';

export function Navbar() {
  const {isAuthenticated, user, logout} = useAuth();
	return (
		<header className='navbar'>
			<div className='navbar-inner'>
				<Link
					to='/'
					className='nav-link'
					style={{fontWeight: 800, fontSize: 18}}>
					🎓 <span style={{color: 'var(--color-accent)'}}>Scholar</span>AI
				</Link>
        <nav style={{display: 'flex', gap: 8, alignItems: 'center'}}>
					<NavLink
						to='/materials'
						className={({isActive}: {isActive: boolean}) =>
							`nav-link${isActive ? ' active' : ''}`
						}>
						Materials
					</NavLink>
					<NavLink
						to='/chat'
						className={({isActive}: {isActive: boolean}) =>
							`nav-link${isActive ? ' active' : ''}`
						}>
						Chat
					</NavLink>
					<NavLink
						to='/quiz'
						className={({isActive}: {isActive: boolean}) =>
							`nav-link${isActive ? ' active' : ''}`
						}>
						Quiz
					</NavLink>
					<NavLink
						to='/dashboard'
						className={({isActive}: {isActive: boolean}) =>
							`nav-link${isActive ? ' active' : ''}`
						}>
						Dashboard
					</NavLink>
					<NavLink
						to='/parental-controls'
						className={({isActive}: {isActive: boolean}) =>
							`nav-link${isActive ? ' active' : ''}`
						}>
						Parental Controls
					</NavLink>
          <span style={{flex: 1}} />
          {isAuthenticated ? (
            <>
              <span className='nav-text'>Hi, {user?.first_name}</span>
              <button className='btn btn-ghost' onClick={() => void logout()}>
                Logout
              </button>
            </>
          ) : (
            <>
              <NavLink
                to='/login'
                className={({isActive}: {isActive: boolean}) =>
                  `nav-link${isActive ? ' active' : ''}`
                }>
                Login
              </NavLink>
              <NavLink
                to='/register'
                className={({isActive}: {isActive: boolean}) =>
                  `nav-link${isActive ? ' active' : ''}`
                }>
                Register
              </NavLink>
            </>
          )}
				</nav>
			</div>
		</header>
	);
}
