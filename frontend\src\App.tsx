import {BrowserRouter, Navigate, Route, Routes} from 'react-router-dom';
import MaterialsPage from '@features/materials/pages/MaterialsPage';
import ChatPage from '@features/chat/pages/ChatPage';
import QuizPage from '@features/quiz/pages/QuizPage';
import DashboardPage from '@features/dashboard/pages/DashboardPage';
import ParentalControlsPage from '@features/coppa/pages/ParentalControlsPage';
import {AuthProvider} from '@shared/auth/AuthContext';
import {RequireAuth, RequireRole} from '@shared/auth/RequireAuth';
import LoginPage from '@features/auth/pages/LoginPage';
import RegisterPage from '@features/auth/pages/RegisterPage';

export default function App() {
	return (
		<AuthProvider>
			<BrowserRouter>
                <Routes>
                    <Route path='/' element={<Navigate to='/materials' replace />} />
                    <Route path='/login' element={<LoginPage />} />
                    <Route path='/register' element={<RegisterPage />} />
                    <Route
                        path='/materials'
                        element={
                            <RequireAuth>
                                <MaterialsPage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path='/chat'
                        element={
                            <RequireAuth>
                                <ChatPage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path='/quiz'
                        element={
                            <RequireAuth>
                                <QuizPage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path='/dashboard'
                        element={
                            <RequireAuth>
                                <DashboardPage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path='/parental-controls'
                        element={
                            <RequireRole roles={['parent', 'admin']}>
                                <ParentalControlsPage />
                            </RequireRole>
                        }
                    />
                    <Route path='*' element={<Navigate to='/materials' replace />} />
                </Routes>
			</BrowserRouter>
		</AuthProvider>
	);
}
